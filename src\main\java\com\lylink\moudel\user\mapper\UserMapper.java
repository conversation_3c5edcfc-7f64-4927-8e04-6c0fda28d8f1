package com.lylink.moudel.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lylink.moudel.user.pojo.User;
import com.lylink.moudel.user.vo.UserInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 用户Mapper接口
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {

    /**
     * 根据手机号查询用户
     * 
     * @param phone 手机号
     * @return 用户信息
     */
    @Select("SELECT * FROM users WHERE phone = #{phone}")
    User findByPhone(@Param("phone") String phone);

    /**
     * 检查手机号是否已存在
     * 
     * @param phone 手机号
     * @return 存在数量
     */
    @Select("SELECT COUNT(*) FROM users WHERE phone = #{phone}")
    int countByPhone(@Param("phone") String phone);

    /**
     * 获取用户详细信息（包含统计数据）
     * 
     * @param userId 用户ID
     * @return 用户详细信息
     */
    UserInfoVO getUserDetailInfo(@Param("userId") Long userId);

    /**
     * 更新用户最后登录时间
     * 
     * @param userId 用户ID
     * @return 更新行数
     */
    @Select("UPDATE users SET updated_at = NOW() WHERE id = #{userId}")
    int updateLastLoginTime(@Param("userId") Long userId);
}
