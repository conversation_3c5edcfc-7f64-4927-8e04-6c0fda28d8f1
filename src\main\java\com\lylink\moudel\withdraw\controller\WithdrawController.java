package com.lylink.moudel.withdraw.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lylink.common.constant.BusinessConstants;
import com.lylink.common.utils.Result;
import com.lylink.common.utils.SecurityContextHandlerUtil;
import com.lylink.moudel.withdraw.dto.WithdrawApplyDTO;
import com.lylink.moudel.withdraw.service.WithdrawService;
import com.lylink.moudel.withdraw.vo.WithdrawRecordVO;
import com.lylink.moudel.withdraw.vo.WithdrawSummaryVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;

/**
 * 提现控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/user/withdraw")
@Tag(name = "提现管理", description = "用户提现申请、记录查询等接口")
public class WithdrawController {

    @Autowired
    private WithdrawService withdrawService;

    /**
     * 获取提现概览
     */
    @GetMapping("/summary")
    @Operation(summary = "获取提现概览", description = "获取用户提现概览信息，包括可提现余额、限额等")
    public Result<WithdrawSummaryVO> getWithdrawSummary() {
        Long userId = SecurityContextHandlerUtil.getUserId();
        WithdrawSummaryVO summary = withdrawService.getUserWithdrawSummary(userId);
        return Result.ok(summary);
    }

    /**
     * 获取提现记录
     */
    @GetMapping("/records")
    @Operation(summary = "获取提现记录", description = "获取用户提现记录列表，支持分页和筛选")
    public Result<IPage<WithdrawRecordVO>> getWithdrawRecords(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "状态筛选") @RequestParam(required = false) String status,
            @Parameter(description = "开始日期") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        Long userId = SecurityContextHandlerUtil.getUserId();
        IPage<WithdrawRecordVO> records = withdrawService.getUserWithdrawRecords(userId, page, size, status, startDate, endDate);
        return Result.ok(records);
    }

    /**
     * 申请提现
     */
    @PostMapping("/apply")
    @Operation(summary = "申请提现", description = "提交提现申请")
    public Result<String> applyWithdraw(@Validated @RequestBody WithdrawApplyDTO applyDTO) {
        Long userId = SecurityContextHandlerUtil.getUserId();
        String applicationId = withdrawService.applyWithdraw(userId, applyDTO);
        return Result.ok(applicationId, "提现申请提交成功，请等待审核");
    }

    /**
     * 取消提现申请
     */
    @PostMapping("/{applicationId}/cancel")
    @Operation(summary = "取消提现申请", description = "取消待审核的提现申请")
    public Result<String> cancelWithdraw(
            @Parameter(description = "申请ID") @PathVariable String applicationId) {
        Long userId = SecurityContextHandlerUtil.getUserId();
        boolean result = withdrawService.cancelWithdraw(userId, applicationId);
        if (result) {
            return Result.ok("提现申请取消成功");
        } else {
            return Result.failMsg("提现申请取消失败");
        }
    }

    /**
     * 检查提现资格
     */
    @GetMapping("/check")
    @Operation(summary = "检查提现资格", description = "检查用户是否可以申请提现")
    public Result<WithdrawService.WithdrawCheckResult> checkWithdrawEligibility() {
        Long userId = SecurityContextHandlerUtil.getUserId();
        WithdrawService.WithdrawCheckResult checkResult = withdrawService.checkWithdrawEligibility(userId);
        return Result.ok(checkResult);
    }

    /**
     * 验证提现金额
     */
    @PostMapping("/validate-amount")
    @Operation(summary = "验证提现金额", description = "验证提现金额是否有效")
    public Result<Boolean> validateWithdrawAmount(
            @Parameter(description = "提现金额") @RequestParam java.math.BigDecimal amount) {
        Long userId = SecurityContextHandlerUtil.getUserId();
        boolean isValid = withdrawService.isValidWithdrawAmount(userId, amount);
        if (isValid) {
            return Result.ok(true, "提现金额有效");
        } else {
            return Result.ok(false, "提现金额无效");
        }
    }
}
