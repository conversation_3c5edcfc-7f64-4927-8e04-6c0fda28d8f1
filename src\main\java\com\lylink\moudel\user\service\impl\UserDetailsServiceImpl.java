package com.lylink.moudel.user.service.impl;

import com.lylink.common.commonVo.UserDetailsVo;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

@Service("UserDetailsServiceImpl")
public class UserDetailsServiceImpl implements UserDetailsService {
    @Override
    public UserDetailsVo loadUserByUsername(String username) throws UsernameNotFoundException {
        return null;
    }
}
