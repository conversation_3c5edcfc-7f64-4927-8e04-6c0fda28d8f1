package com.lylink.moudel.withdraw.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 提现概览VO
 */
@Data
public class WithdrawSummaryVO {

    /**
     * 可提现余额
     */
    private BigDecimal availableBalance;

    /**
     * 最小提现金额
     */
    private BigDecimal minAmount;

    /**
     * 最大提现金额
     */
    private BigDecimal maxAmount;

    /**
     * 提现手续费率
     */
    private BigDecimal feeRate;

    /**
     * 今日已提现金额
     */
    private BigDecimal todayWithdrawn;

    /**
     * 今日剩余提现额度
     */
    private BigDecimal todayRemaining;

    /**
     * 总提现金额
     */
    private BigDecimal totalWithdrawn;

    /**
     * 待审核申请数
     */
    private Integer pendingCount;

    /**
     * 处理中申请数
     */
    private Integer processingCount;

    /**
     * 是否可以提现
     */
    private Boolean canWithdraw;

    /**
     * 不能提现的原因
     */
    private String cannotWithdrawReason;
}
