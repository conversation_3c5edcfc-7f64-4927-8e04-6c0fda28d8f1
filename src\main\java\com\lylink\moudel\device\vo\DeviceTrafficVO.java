package com.lylink.moudel.device.vo;

import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * 设备流量统计VO
 */
@Data
public class DeviceTrafficVO {

    /**
     * 设备序列号
     */
    private String sn;

    /**
     * 今日流量(MB)
     */
    private Long todayTraffic;

    /**
     * 本月流量(MB)
     */
    private Long monthTraffic;

    /**
     * 总流量(MB)
     */
    private Long totalTraffic;

    /**
     * 流量趋势数据
     */
    private List<TrafficTrendItem> trendData;

    @Data
    public static class TrafficTrendItem {
        /**
         * 日期
         */
        private LocalDate date;

        /**
         * 流量(MB)
         */
        private Long traffic;

        /**
         * 在线时长(分钟)
         */
        private Integer onlineDuration;
    }
}
