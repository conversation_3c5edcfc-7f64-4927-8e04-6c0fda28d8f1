package com.lylink.moudel.user.controller;

import com.lylink.common.constant.BusinessConstants;
import com.lylink.common.utils.Result;
import com.lylink.common.utils.SecurityContextHandlerUtil;
import com.lylink.moudel.user.dto.*;
import com.lylink.moudel.user.service.UserService;
import com.lylink.moudel.user.vo.LoginResultVO;
import com.lylink.moudel.user.vo.UserInfoVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 用户控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/user")
@Tag(name = "用户管理", description = "用户注册、登录、信息管理等接口")
public class UserController {

    @Autowired
    private UserService userService;

    /**
     * 发送短信验证码
     */
    @PostMapping("/sms/send")
    @Operation(summary = "发送短信验证码", description = "发送注册、登录、重置密码等验证码")
    public Result<String> sendSmsCode(@Validated @RequestBody SmsCodeDTO smsCodeDTO) {
        boolean result = userService.sendSmsCode(smsCodeDTO);
        if (result) {
            return Result.ok(BusinessConstants.ResponseMessage.SMS_SEND_SUCCESS);
        } else {
            return Result.failMsg(BusinessConstants.ResponseMessage.SMS_SEND_FAILED);
        }
    }

    /**
     * 用户注册
     */
    @PostMapping("/register")
    @Operation(summary = "用户注册", description = "通过手机号和验证码注册新用户")
    public Result<String> register(@Validated @RequestBody UserRegisterDTO registerDTO) {
        boolean result = userService.register(registerDTO);
        if (result) {
            return Result.ok(BusinessConstants.ResponseMessage.REGISTER_SUCCESS);
        } else {
            return Result.failMsg(BusinessConstants.ResponseMessage.REGISTER_FAILED);
        }
    }

    /**
     * 用户登录
     */
    @PostMapping("/login")
    @Operation(summary = "用户登录", description = "支持密码登录和验证码登录两种方式")
    public Result<LoginResultVO> login(@Validated @RequestBody UserLoginDTO loginDTO) {
        LoginResultVO result = userService.login(loginDTO);
        return Result.ok(result);
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/info")
    @Operation(summary = "获取用户信息", description = "获取当前登录用户的详细信息")
    public Result<UserInfoVO> getUserInfo() {
        Long userId = SecurityContextHandlerUtil.getUserId();
        UserInfoVO userInfo = userService.getUserInfo(userId);
        return Result.ok(userInfo);
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/info/update")
    @Operation(summary = "更新用户信息", description = "更新用户昵称、支付宝信息等")
    public Result<String> updateUserInfo(@Validated @RequestBody UserUpdateDTO updateDTO) {
        Long userId = SecurityContextHandlerUtil.getUserId();
        boolean result = userService.updateUserInfo(userId, updateDTO);
        if (result) {
            return Result.ok(BusinessConstants.ResponseMessage.SUCCESS);
        } else {
            return Result.failMsg(BusinessConstants.ResponseMessage.FAILED);
        }
    }

    /**
     * 重置密码
     */
    @PostMapping("/password/reset")
    @Operation(summary = "重置密码", description = "通过手机号和验证码重置密码")
    public Result<String> resetPassword(@Validated @RequestBody PasswordResetDTO resetDTO) {
        boolean result = userService.resetPassword(resetDTO);
        if (result) {
            return Result.ok("密码重置成功");
        } else {
            return Result.failMsg("密码重置失败");
        }
    }

    /**
     * 刷新Token
     */
    @PostMapping("/token/refresh")
    @Operation(summary = "刷新Token", description = "刷新用户访问令牌")
    public Result<String> refreshToken() {
        Long userId = SecurityContextHandlerUtil.getUserId();
        String newToken = userService.refreshToken(userId);
        return Result.ok(newToken);
    }

    /**
     * 用户退出登录
     */
    @PostMapping("/logout")
    @Operation(summary = "用户退出", description = "用户退出登录")
    public Result<String> logout() {
        // 这里可以实现token黑名单机制
        // 或者清除Redis中的用户缓存
        Long userId = SecurityContextHandlerUtil.getUserId();
        log.info("用户退出登录: userId={}", userId);
        return Result.ok("退出成功");
    }
}
