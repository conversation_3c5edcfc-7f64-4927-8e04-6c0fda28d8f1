package com.lylink.moudel.device.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lylink.moudel.device.dto.DeviceBindDTO;
import com.lylink.moudel.device.dto.DeviceUpdateDTO;
import com.lylink.moudel.device.pojo.Device;
import com.lylink.moudel.device.vo.DeviceBenchmarkVO;
import com.lylink.moudel.device.vo.DeviceInfoVO;
import com.lylink.moudel.device.vo.DeviceTrafficVO;

/**
 * 设备服务接口
 */
public interface DeviceService extends IService<Device> {

    /**
     * 获取用户设备列表
     * 
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 设备列表
     */
    IPage<DeviceInfoVO> getUserDevices(Long userId, int page, int size);

    /**
     * 获取设备详情
     * 
     * @param sn 设备序列号
     * @param userId 用户ID
     * @return 设备详情
     */
    DeviceInfoVO getDeviceDetail(String sn, Long userId);

    /**
     * 绑定设备
     * 
     * @param userId 用户ID
     * @param bindDTO 绑定信息
     * @return 绑定结果
     */
    boolean bindDevice(Long userId, DeviceBindDTO bindDTO);

    /**
     * 解绑设备
     * 
     * @param sn 设备序列号
     * @param userId 用户ID
     * @return 解绑结果
     */
    boolean unbindDevice(String sn, Long userId);

    /**
     * 更新设备信息
     * 
     * @param userId 用户ID
     * @param updateDTO 更新信息
     * @return 更新结果
     */
    boolean updateDevice(Long userId, DeviceUpdateDTO updateDTO);

    /**
     * 获取设备流量统计
     * 
     * @param sn 设备序列号
     * @param userId 用户ID
     * @param days 统计天数
     * @return 流量统计
     */
    DeviceTrafficVO getDeviceTraffic(String sn, Long userId, int days);

    /**
     * 获取设备性能测试结果
     * 
     * @param sn 设备序列号
     * @param userId 用户ID
     * @return 性能测试结果
     */
    DeviceBenchmarkVO getDeviceBenchmark(String sn, Long userId);

    /**
     * 启动设备性能测试
     * 
     * @param sn 设备序列号
     * @param userId 用户ID
     * @return 启动结果
     */
    boolean startDeviceBenchmark(String sn, Long userId);

    /**
     * 根据序列号查询设备
     * 
     * @param sn 设备序列号
     * @return 设备信息
     */
    Device findBySn(String sn);

    /**
     * 检查设备是否存在
     * 
     * @param sn 设备序列号
     * @return 是否存在
     */
    boolean isDeviceExists(String sn);

    /**
     * 检查设备是否已绑定
     * 
     * @param sn 设备序列号
     * @return 是否已绑定
     */
    boolean isDeviceBound(String sn);

    /**
     * 检查用户是否可以绑定更多设备
     * 
     * @param userId 用户ID
     * @return 是否可以绑定
     */
    boolean canBindMoreDevices(Long userId);

    /**
     * 同步设备状态
     * 
     * @param sn 设备序列号
     * @return 同步结果
     */
    boolean syncDeviceStatus(String sn);

    /**
     * 批量同步所有设备状态
     * 
     * @return 同步结果
     */
    boolean syncAllDeviceStatus();
}
