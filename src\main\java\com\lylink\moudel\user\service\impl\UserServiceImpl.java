package com.lylink.moudel.user.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lylink.common.config.BusinessConfig;
import com.lylink.common.constant.BusinessConstants;
import com.lylink.common.exception.CustomaizeExpetion;
import com.lylink.common.exception.ExceptionEnum;
import com.lylink.common.utils.*;
import com.lylink.moudel.user.dto.*;
import com.lylink.moudel.user.mapper.SmsCodeMapper;
import com.lylink.moudel.user.mapper.UserMapper;
import com.lylink.moudel.user.pojo.SmsCode;
import com.lylink.moudel.user.pojo.User;
import com.lylink.moudel.user.service.SmsService;
import com.lylink.moudel.user.service.UserService;
import com.lylink.moudel.user.vo.LoginResultVO;
import com.lylink.moudel.user.vo.UserInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 用户服务实现类
 */
@Slf4j
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private SmsCodeMapper smsCodeMapper;

    @Autowired
    private SmsService smsService;

    @Autowired
    private BusinessConfig businessConfig;

    @Autowired
    private JwtUtils jwtUtils;

    @Autowired
    private RedisUtils redisUtils;

    @Override
    public boolean sendSmsCode(SmsCodeDTO smsCodeDTO) {
        String phone = smsCodeDTO.getPhone();
        String type = smsCodeDTO.getType();

        // 验证手机号格式
        if (!PhoneUtils.isValidPhone(phone)) {
            throw new CustomaizeExpetion(ExceptionEnum.INVALID_PHONE);
        }

        // 检查发送频率限制
        if (!smsService.checkRateLimit(phone)) {
            throw new CustomaizeExpetion(ExceptionEnum.SMS_SEND_TOO_FREQUENT);
        }

        // 检查每日发送次数限制
        if (!smsService.checkDailyLimit(phone)) {
            throw new CustomaizeExpetion(ExceptionEnum.SMS_DAILY_LIMIT_EXCEEDED);
        }

        // 注册时检查手机号是否已存在
        if (BusinessConstants.SmsCodeType.REGISTER.equals(type) && isPhoneExists(phone)) {
            throw new CustomaizeExpetion(ExceptionEnum.PHONE_ALREADY_EXISTS);
        }

        // 登录和重置密码时检查用户是否存在
        if ((BusinessConstants.SmsCodeType.LOGIN.equals(type) || 
             BusinessConstants.SmsCodeType.RESET_PASSWORD.equals(type)) && !isPhoneExists(phone)) {
            throw new CustomaizeExpetion(ExceptionEnum.USER_NOT_FOUND);
        }

        // 生成验证码
        String code = SmsCodeUtils.generateCode();
        SmsCodeUtils.CodeType codeType = SmsCodeUtils.CodeType.fromCode(type);

        // 发送短信
        boolean sendResult = smsService.sendSmsCode(phone, code, codeType);
        if (!sendResult) {
            throw new CustomaizeExpetion(ExceptionEnum.SMS_SEND_FAILED);
        }

        // 保存验证码到数据库
        SmsCode smsCode = new SmsCode();
        smsCode.setPhone(phone);
        smsCode.setCode(code);
        smsCode.setType(type);
        smsCode.setUsed(0);
        smsCode.setExpireTime(LocalDateTime.now().plusMinutes(businessConfig.getSmsCode().getExpireMinutes()));

        return smsCodeMapper.insert(smsCode) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean register(UserRegisterDTO registerDTO) {
        String phone = registerDTO.getPhone();
        String password = registerDTO.getPassword();
        String confirmPassword = registerDTO.getConfirmPassword();
        String smsCode = registerDTO.getSmsCode();

        // 验证手机号格式
        if (!PhoneUtils.isValidPhone(phone)) {
            throw new CustomaizeExpetion(ExceptionEnum.INVALID_PHONE);
        }

        // 验证密码格式
        if (!PasswordUtils.isValidPassword(password)) {
            throw new CustomaizeExpetion(ExceptionEnum.INVALID_PASSWORD);
        }

        // 验证密码一致性
        if (!password.equals(confirmPassword)) {
            throw new CustomaizeExpetion(ExceptionEnum.PASSWORD_NOT_MATCH);
        }

        // 检查手机号是否已存在
        if (isPhoneExists(phone)) {
            throw new CustomaizeExpetion(ExceptionEnum.PHONE_ALREADY_EXISTS);
        }

        // 验证短信验证码
        if (!verifySmsCode(phone, smsCode, BusinessConstants.SmsCodeType.REGISTER)) {
            throw new CustomaizeExpetion(ExceptionEnum.SMS_CODE_INVALID);
        }

        // 创建用户
        User user = new User();
        user.setPhone(phone);
        user.setPasswordHash(PasswordUtils.encode(password));
        user.setNickname(StringUtils.isEmpty(registerDTO.getNickname()) ? 
                        BusinessConstants.DefaultValues.DEFAULT_NICKNAME + phone.substring(7) : 
                        registerDTO.getNickname());
        user.setCommissionRate(businessConfig.getUser().getDefaultCommissionRate());
        user.setSettlementCycle(BusinessConstants.DefaultValues.DEFAULT_SETTLEMENT_CYCLE);
        user.setStatus(BusinessConstants.UserStatus.NORMAL);

        boolean result = save(user);
        if (result) {
            log.info("用户注册成功: {}", phone);
        }

        return result;
    }

    @Override
    public LoginResultVO login(UserLoginDTO loginDTO) {
        String phone = loginDTO.getPhone();
        String loginType = loginDTO.getLoginType();

        // 验证手机号格式
        if (!PhoneUtils.isValidPhone(phone)) {
            throw new CustomaizeExpetion(ExceptionEnum.INVALID_PHONE);
        }

        // 查询用户
        User user = findByPhone(phone);
        if (user == null) {
            throw new CustomaizeExpetion(ExceptionEnum.USER_NOT_FOUND);
        }

        // 检查用户状态
        if (!BusinessConstants.UserStatus.NORMAL.equals(user.getStatus())) {
            throw new CustomaizeExpetion(ExceptionEnum.USER_DISABLED);
        }

        // 验证登录凭证
        if ("password".equals(loginType)) {
            // 密码登录
            String password = loginDTO.getPassword();
            if (StringUtils.isEmpty(password)) {
                throw new CustomaizeExpetion(ExceptionEnum.INVALID_PASSWORD);
            }
            if (!PasswordUtils.matches(password, user.getPasswordHash())) {
                throw new CustomaizeExpetion(ExceptionEnum.LOGIN_FAIL);
            }
        } else if ("sms".equals(loginType)) {
            // 验证码登录
            String smsCode = loginDTO.getSmsCode();
            if (!verifySmsCode(phone, smsCode, BusinessConstants.SmsCodeType.LOGIN)) {
                throw new CustomaizeExpetion(ExceptionEnum.SMS_CODE_INVALID);
            }
        } else {
            throw new CustomaizeExpetion(ExceptionEnum.FAIL_RME_PARAM);
        }

        // 更新最后登录时间
        userMapper.updateLastLoginTime(user.getId());

        // 生成JWT token
        String token = jwtUtils.generateToken(user.getId().toString());

        // 构建返回结果
        LoginResultVO result = new LoginResultVO();
        result.setAccessToken(token);
        result.setExpiresIn(jwtUtils.getExpirationTime() * 3600L); // 转换为秒
        result.setUserInfo(getUserInfo(user.getId()));
        result.setIsFirstLogin(user.getCreatedAt().isAfter(LocalDateTime.now().minusMinutes(5)));

        log.info("用户登录成功: {}", phone);
        return result;
    }

    @Override
    public UserInfoVO getUserInfo(Long userId) {
        // 先尝试从数据库获取详细信息
        UserInfoVO userInfo = userMapper.getUserDetailInfo(userId);
        if (userInfo == null) {
            User user = getById(userId);
            if (user == null) {
                throw new CustomaizeExpetion(ExceptionEnum.USER_NOT_FOUND);
            }

            // 构建基本用户信息
            userInfo = new UserInfoVO();
            BeanUtils.copyProperties(user, userInfo);
            userInfo.setPhone(PhoneUtils.maskPhone(user.getPhone()));
            userInfo.setAlipayAccount(maskAlipayAccount(user.getAlipayAccount()));
            userInfo.setDeviceCount(0);
            userInfo.setOnlineDeviceCount(0);
            userInfo.setTodayIncome(java.math.BigDecimal.ZERO);
            userInfo.setTotalIncome(java.math.BigDecimal.ZERO);
            userInfo.setAvailableBalance(java.math.BigDecimal.ZERO);
        } else {
            // 脱敏处理
            userInfo.setPhone(PhoneUtils.maskPhone(userInfo.getPhone()));
            userInfo.setAlipayAccount(maskAlipayAccount(userInfo.getAlipayAccount()));
        }

        return userInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUserInfo(Long userId, UserUpdateDTO updateDTO) {
        User user = getById(userId);
        if (user == null) {
            throw new CustomaizeExpetion(ExceptionEnum.USER_NOT_FOUND);
        }

        // 更新用户信息
        if (!StringUtils.isEmpty(updateDTO.getNickname())) {
            user.setNickname(updateDTO.getNickname());
        }
        if (!StringUtils.isEmpty(updateDTO.getAlipayAccount())) {
            user.setAlipayAccount(updateDTO.getAlipayAccount());
        }
        if (!StringUtils.isEmpty(updateDTO.getAlipayName())) {
            user.setAlipayName(updateDTO.getAlipayName());
        }

        boolean result = updateById(user);
        if (result) {
            log.info("用户信息更新成功: userId={}", userId);
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean resetPassword(PasswordResetDTO resetDTO) {
        String phone = resetDTO.getPhone();
        String newPassword = resetDTO.getNewPassword();
        String confirmPassword = resetDTO.getConfirmPassword();
        String smsCode = resetDTO.getSmsCode();

        // 验证手机号格式
        if (!PhoneUtils.isValidPhone(phone)) {
            throw new CustomaizeExpetion(ExceptionEnum.INVALID_PHONE);
        }

        // 验证密码格式
        if (!PasswordUtils.isValidPassword(newPassword)) {
            throw new CustomaizeExpetion(ExceptionEnum.INVALID_PASSWORD);
        }

        // 验证密码一致性
        if (!newPassword.equals(confirmPassword)) {
            throw new CustomaizeExpetion(ExceptionEnum.PASSWORD_NOT_MATCH);
        }

        // 查询用户
        User user = findByPhone(phone);
        if (user == null) {
            throw new CustomaizeExpetion(ExceptionEnum.USER_NOT_FOUND);
        }

        // 验证短信验证码
        if (!verifySmsCode(phone, smsCode, BusinessConstants.SmsCodeType.RESET_PASSWORD)) {
            throw new CustomaizeExpetion(ExceptionEnum.SMS_CODE_INVALID);
        }

        // 更新密码
        user.setPasswordHash(PasswordUtils.encode(newPassword));
        boolean result = updateById(user);
        if (result) {
            log.info("密码重置成功: {}", phone);
        }

        return result;
    }

    @Override
    public User findByPhone(String phone) {
        return userMapper.findByPhone(phone);
    }

    @Override
    public boolean isPhoneExists(String phone) {
        return userMapper.countByPhone(phone) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean verifySmsCode(String phone, String code, String type) {
        if (StringUtils.isEmpty(phone) || StringUtils.isEmpty(code) || StringUtils.isEmpty(type)) {
            return false;
        }

        // 查询最新的有效验证码
        SmsCode smsCode = smsCodeMapper.findLatestValidCode(phone, type);
        if (smsCode == null) {
            return false;
        }

        // 验证验证码
        if (!code.equals(smsCode.getCode())) {
            return false;
        }

        // 检查是否过期
        if (LocalDateTime.now().isAfter(smsCode.getExpireTime())) {
            return false;
        }

        // 标记为已使用
        smsCodeMapper.markAsUsed(smsCode.getId());
        return true;
    }

    @Override
    public String refreshToken(Long userId) {
        User user = getById(userId);
        if (user == null) {
            throw new CustomaizeExpetion(ExceptionEnum.USER_NOT_FOUND);
        }

        if (!BusinessConstants.UserStatus.NORMAL.equals(user.getStatus())) {
            throw new CustomaizeExpetion(ExceptionEnum.USER_DISABLED);
        }

        return jwtUtils.generateToken(userId.toString());
    }

    /**
     * 支付宝账号脱敏处理
     *
     * @param alipayAccount 支付宝账号
     * @return 脱敏后的账号
     */
    private String maskAlipayAccount(String alipayAccount) {
        if (StringUtils.isEmpty(alipayAccount)) {
            return alipayAccount;
        }

        if (PhoneUtils.isValidPhone(alipayAccount)) {
            return PhoneUtils.maskPhone(alipayAccount);
        } else if (alipayAccount.contains("@")) {
            // 邮箱脱敏
            int atIndex = alipayAccount.indexOf("@");
            if (atIndex > 3) {
                return alipayAccount.substring(0, 3) + "****" + alipayAccount.substring(atIndex);
            }
        }

        return alipayAccount;
    }
}
