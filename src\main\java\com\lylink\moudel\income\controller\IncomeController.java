package com.lylink.moudel.income.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lylink.common.utils.Result;
import com.lylink.common.utils.SecurityContextHandlerUtil;
import com.lylink.moudel.income.service.IncomeService;
import com.lylink.moudel.income.vo.IncomeChartVO;
import com.lylink.moudel.income.vo.IncomeDetailVO;
import com.lylink.moudel.income.vo.IncomeSummaryVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;

/**
 * 收益控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/user/income")
@Tag(name = "收益管理", description = "用户收益查询、统计等接口")
public class IncomeController {

    @Autowired
    private IncomeService incomeService;

    /**
     * 获取收益概览
     */
    @GetMapping("/summary")
    @Operation(summary = "获取收益概览", description = "获取用户收益概览信息，包括今日、本月、总收益等")
    public Result<IncomeSummaryVO> getIncomeSummary() {
        Long userId = SecurityContextHandlerUtil.getUserId();
        IncomeSummaryVO summary = incomeService.getUserIncomeSummary(userId);
        return Result.ok(summary);
    }

    /**
     * 获取收益明细
     */
    @GetMapping("/detail")
    @Operation(summary = "获取收益明细", description = "获取用户收益明细列表，支持分页和筛选")
    public Result<IPage<IncomeDetailVO>> getIncomeDetail(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") int page,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "设备序列号") @RequestParam(required = false) String deviceSn,
            @Parameter(description = "开始日期") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate startDate,
            @Parameter(description = "结束日期") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate endDate) {
        Long userId = SecurityContextHandlerUtil.getUserId();
        IPage<IncomeDetailVO> details = incomeService.getUserIncomeDetails(userId, page, size, deviceSn, startDate, endDate);
        return Result.ok(details);
    }

    /**
     * 获取收益趋势图
     */
    @GetMapping("/chart")
    @Operation(summary = "获取收益趋势图", description = "获取收益趋势图数据，支持日收益和月收益")
    public Result<IncomeChartVO> getIncomeChart(
            @Parameter(description = "图表类型：daily-日收益，monthly-月收益", example = "daily") 
            @RequestParam(defaultValue = "daily") String chartType,
            @Parameter(description = "统计天数/月数", example = "7") 
            @RequestParam(defaultValue = "7") int days) {
        Long userId = SecurityContextHandlerUtil.getUserId();
        IncomeChartVO chart = incomeService.getIncomeChart(userId, chartType, days);
        return Result.ok(chart);
    }

    /**
     * 手动同步收益
     */
    @PostMapping("/sync")
    @Operation(summary = "手动同步收益", description = "手动触发收益数据同步")
    public Result<String> syncIncome(
            @Parameter(description = "同步日期") @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate date) {
        if (date == null) {
            date = LocalDate.now().minusDays(1); // 默认同步昨天的数据
        }
        
        boolean result = incomeService.syncAllUserIncome(date);
        if (result) {
            return Result.ok("收益同步成功");
        } else {
            return Result.failMsg("收益同步失败");
        }
    }
}
