package com.lylink.moudel.income.pojo;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 收益记录实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("income_records")
public class IncomeRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 设备序列号
     */
    @TableField("device_sn")
    private String deviceSn;

    /**
     * 收益日期
     */
    @TableField("date")
    private LocalDate date;

    /**
     * 上游收益
     */
    @TableField("upstream_income")
    private BigDecimal upstreamIncome;

    /**
     * 平台抽成
     */
    @TableField("commission")
    private BigDecimal commission;

    /**
     * 实际收益
     */
    @TableField("actual_income")
    private BigDecimal actualIncome;

    /**
     * 流量消耗(MB)
     */
    @TableField("traffic_mb")
    private Long trafficMb;

    /**
     * 在线时长(分钟)
     */
    @TableField("online_duration")
    private Integer onlineDuration;

    /**
     * 同步状态：0-未同步，1-已同步
     */
    @TableField("sync_status")
    private Integer syncStatus;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}
