package com.lylink.moudel.user.service;

import com.lylink.common.utils.SmsCodeUtils;

/**
 * 短信服务接口
 */
public interface SmsService {

    /**
     * 发送短信验证码
     * 
     * @param phone 手机号
     * @param code 验证码
     * @param type 验证码类型
     * @return 发送结果
     */
    boolean sendSmsCode(String phone, String code, SmsCodeUtils.CodeType type);

    /**
     * 发送短信验证码（使用模板）
     * 
     * @param phone 手机号
     * @param templateCode 模板代码
     * @param params 模板参数
     * @return 发送结果
     */
    boolean sendSmsWithTemplate(String phone, String templateCode, String... params);

    /**
     * 检查发送频率限制
     * 
     * @param phone 手机号
     * @return 是否可以发送
     */
    boolean checkRateLimit(String phone);

    /**
     * 检查每日发送次数限制
     * 
     * @param phone 手机号
     * @return 是否可以发送
     */
    boolean checkDailyLimit(String phone);

    /**
     * 获取剩余冷却时间（秒）
     * 
     * @param phone 手机号
     * @return 剩余冷却时间
     */
    long getRemainingCooldown(String phone);
}
