package com.lylink.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 短信配置类
 */
@Data
@Component
@ConfigurationProperties(prefix = "sms")
public class SmsConfig {

    /**
     * 短信服务商
     */
    private String provider = "aliyun";

    /**
     * 访问密钥ID
     */
    private String accessKey;

    /**
     * 访问密钥Secret
     */
    private String accessSecret;

    /**
     * 短信签名
     */
    private String signName = "灵源Link";

    /**
     * 模板代码配置
     */
    private TemplateCode templateCode = new TemplateCode();

    @Data
    public static class TemplateCode {
        /**
         * 注册验证码模板
         */
        private String register = "SMS_123456789";

        /**
         * 登录验证码模板
         */
        private String login = "SMS_123456790";

        /**
         * 重置密码验证码模板
         */
        private String reset = "SMS_123456791";

        /**
         * 绑定设备验证码模板
         */
        private String bindDevice = "SMS_123456792";

        /**
         * 提现验证码模板
         */
        private String withdraw = "SMS_123456793";

        /**
         * 根据类型获取模板代码
         * 
         * @param type 验证码类型
         * @return 模板代码
         */
        public String getTemplateByType(String type) {
            switch (type) {
                case "register":
                    return register;
                case "login":
                    return login;
                case "reset_password":
                    return reset;
                case "bind_device":
                    return bindDevice;
                case "withdraw":
                    return withdraw;
                default:
                    return register; // 默认使用注册模板
            }
        }
    }

    /**
     * 短信服务商枚举
     */
    public enum Provider {
        ALIYUN("aliyun", "阿里云"),
        TENCENT("tencent", "腾讯云"),
        HUAWEI("huawei", "华为云"),
        MOCK("mock", "模拟发送");

        private final String code;
        private final String name;

        Provider(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public String getCode() {
            return code;
        }

        public String getName() {
            return name;
        }

        public static Provider fromCode(String code) {
            for (Provider provider : values()) {
                if (provider.code.equals(code)) {
                    return provider;
                }
            }
            return MOCK; // 默认使用模拟发送
        }
    }

    /**
     * 获取短信服务商
     * 
     * @return 短信服务商枚举
     */
    public Provider getProviderEnum() {
        return Provider.fromCode(provider);
    }

    /**
     * 是否为模拟发送模式
     * 
     * @return true-模拟发送，false-真实发送
     */
    public boolean isMockMode() {
        return Provider.MOCK.equals(getProviderEnum());
    }
}
