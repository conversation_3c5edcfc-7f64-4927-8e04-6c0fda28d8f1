<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lylink.moudel.withdraw.mapper.WithdrawApplicationMapper">

    <!-- 提现概览结果映射 -->
    <resultMap id="WithdrawSummaryMap" type="com.lylink.moudel.withdraw.vo.WithdrawSummaryVO">
        <result column="available_balance" property="availableBalance"/>
        <result column="today_withdrawn" property="todayWithdrawn"/>
        <result column="total_withdrawn" property="totalWithdrawn"/>
        <result column="pending_count" property="pendingCount"/>
        <result column="processing_count" property="processingCount"/>
    </resultMap>

    <!-- 提现记录结果映射 -->
    <resultMap id="WithdrawRecordMap" type="com.lylink.moudel.withdraw.vo.WithdrawRecordVO">
        <id column="id" property="id"/>
        <result column="amount" property="amount"/>
        <result column="alipay_account" property="alipayAccount"/>
        <result column="alipay_name" property="alipayName"/>
        <result column="status" property="status"/>
        <result column="status_text" property="statusText"/>
        <result column="audit_reason" property="auditReason"/>
        <result column="apply_time" property="applyTime"/>
        <result column="audit_time" property="auditTime"/>
        <result column="process_time" property="processTime"/>
        <result column="remark" property="remark"/>
        <result column="processing_hours" property="processingHours"/>
    </resultMap>

    <!-- 获取用户提现概览 -->
    <select id="getUserWithdrawSummary" resultMap="WithdrawSummaryMap">
        SELECT 
            COALESCE(income_stats.available_balance, 0) as available_balance,
            COALESCE(today_stats.today_withdrawn, 0) as today_withdrawn,
            COALESCE(total_stats.total_withdrawn, 0) as total_withdrawn,
            COALESCE(pending_stats.pending_count, 0) as pending_count,
            COALESCE(processing_stats.processing_count, 0) as processing_count
        FROM (SELECT 1) as dummy
        LEFT JOIN (
            SELECT 
                COALESCE(SUM(ir.actual_income), 0) - COALESCE(SUM(wa.amount), 0) as available_balance
            FROM income_records ir
            LEFT JOIN withdraw_applications wa ON ir.user_id = wa.user_id AND wa.status IN ('approved', 'completed')
            WHERE ir.user_id = #{userId}
        ) income_stats ON 1=1
        LEFT JOIN (
            SELECT 
                SUM(amount) as today_withdrawn
            FROM withdraw_applications 
            WHERE user_id = #{userId} AND DATE(apply_time) = CURDATE() 
                AND status IN ('approved', 'processing', 'completed')
        ) today_stats ON 1=1
        LEFT JOIN (
            SELECT 
                SUM(amount) as total_withdrawn
            FROM withdraw_applications 
            WHERE user_id = #{userId} AND status IN ('approved', 'completed')
        ) total_stats ON 1=1
        LEFT JOIN (
            SELECT 
                COUNT(*) as pending_count
            FROM withdraw_applications 
            WHERE user_id = #{userId} AND status = 'pending'
        ) pending_stats ON 1=1
        LEFT JOIN (
            SELECT 
                COUNT(*) as processing_count
            FROM withdraw_applications 
            WHERE user_id = #{userId} AND status = 'processing'
        ) processing_stats ON 1=1
    </select>

    <!-- 获取用户提现记录 -->
    <select id="getUserWithdrawRecords" resultMap="WithdrawRecordMap">
        SELECT 
            wa.id,
            wa.amount,
            CASE 
                WHEN wa.alipay_account LIKE '%@%' THEN 
                    CONCAT(SUBSTRING(wa.alipay_account, 1, 3), '****', SUBSTRING(wa.alipay_account, LOCATE('@', wa.alipay_account)))
                ELSE 
                    CONCAT(SUBSTRING(wa.alipay_account, 1, 3), '****', SUBSTRING(wa.alipay_account, -4))
            END as alipay_account,
            wa.alipay_name,
            wa.status,
            CASE wa.status
                WHEN 'pending' THEN '待审核'
                WHEN 'approved' THEN '已通过'
                WHEN 'rejected' THEN '已拒绝'
                WHEN 'processing' THEN '处理中'
                WHEN 'completed' THEN '已完成'
                ELSE '未知'
            END as status_text,
            wa.audit_reason,
            wa.apply_time,
            wa.audit_time,
            wa.process_time,
            wa.remark,
            CASE 
                WHEN wa.process_time IS NOT NULL THEN 
                    TIMESTAMPDIFF(HOUR, wa.apply_time, wa.process_time)
                WHEN wa.audit_time IS NOT NULL THEN 
                    TIMESTAMPDIFF(HOUR, wa.apply_time, wa.audit_time)
                ELSE 
                    TIMESTAMPDIFF(HOUR, wa.apply_time, NOW())
            END as processing_hours
        FROM withdraw_applications wa
        WHERE wa.user_id = #{userId}
        <if test="status != null and status != ''">
            AND wa.status = #{status}
        </if>
        <if test="startDate != null">
            AND DATE(wa.apply_time) >= #{startDate}
        </if>
        <if test="endDate != null">
            AND DATE(wa.apply_time) <= #{endDate}
        </if>
        ORDER BY wa.apply_time DESC
    </select>

</mapper>
