package com.lylink.moudel.user.dto;

import lombok.Data;

import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 用户信息更新DTO
 */
@Data
public class UserUpdateDTO {

    /**
     * 昵称
     */
    @Size(max = 50, message = "昵称长度不能超过50个字符")
    private String nickname;

    /**
     * 支付宝账号
     */
    @Pattern(regexp = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}|^1[3-9]\\d{9}$", 
             message = "支付宝账号格式不正确")
    private String alipayAccount;

    /**
     * 支付宝姓名
     */
    @Size(max = 50, message = "支付宝姓名长度不能超过50个字符")
    private String alipayName;
}
