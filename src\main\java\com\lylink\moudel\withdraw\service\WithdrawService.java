package com.lylink.moudel.withdraw.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.lylink.moudel.withdraw.dto.WithdrawApplyDTO;
import com.lylink.moudel.withdraw.pojo.WithdrawApplication;
import com.lylink.moudel.withdraw.vo.WithdrawRecordVO;
import com.lylink.moudel.withdraw.vo.WithdrawSummaryVO;

import java.time.LocalDate;

/**
 * 提现服务接口
 */
public interface WithdrawService extends IService<WithdrawApplication> {

    /**
     * 获取用户提现概览
     * 
     * @param userId 用户ID
     * @return 提现概览
     */
    WithdrawSummaryVO getUserWithdrawSummary(Long userId);

    /**
     * 获取用户提现记录
     * 
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @param status 状态（可选）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 提现记录列表
     */
    IPage<WithdrawRecordVO> getUserWithdrawRecords(Long userId, int page, int size, 
                                                   String status, LocalDate startDate, LocalDate endDate);

    /**
     * 申请提现
     * 
     * @param userId 用户ID
     * @param applyDTO 申请信息
     * @return 申请结果
     */
    String applyWithdraw(Long userId, WithdrawApplyDTO applyDTO);

    /**
     * 取消提现申请
     * 
     * @param userId 用户ID
     * @param applicationId 申请ID
     * @return 取消结果
     */
    boolean cancelWithdraw(Long userId, String applicationId);

    /**
     * 检查用户是否可以提现
     * 
     * @param userId 用户ID
     * @return 检查结果
     */
    WithdrawCheckResult checkWithdrawEligibility(Long userId);

    /**
     * 检查提现金额是否有效
     * 
     * @param userId 用户ID
     * @param amount 提现金额
     * @return 检查结果
     */
    boolean isValidWithdrawAmount(Long userId, java.math.BigDecimal amount);

    /**
     * 提现检查结果
     */
    class WithdrawCheckResult {
        private boolean canWithdraw;
        private String reason;

        public WithdrawCheckResult(boolean canWithdraw, String reason) {
            this.canWithdraw = canWithdraw;
            this.reason = reason;
        }

        public boolean isCanWithdraw() {
            return canWithdraw;
        }

        public String getReason() {
            return reason;
        }
    }
}
