# 灵源Link设备管理与收益分发平台

## 项目简介

灵源Link是一个设备管理与收益分发平台的小程序后端系统，主要功能包括：

- 用户注册登录管理
- 设备绑定与状态监控
- 收益计算与分发
- 提现申请与处理
- 数据同步与定时任务

## 技术栈

- **框架**: Spring Boot 3.5.3
- **数据库**: MySQL 8.0+
- **缓存**: Redis
- **ORM**: MyBatis-Plus
- **安全**: Spring Security + JWT
- **文档**: Swagger/OpenAPI 3
- **构建工具**: Maven

## 项目结构

```
src/main/java/com/lylink/
├── LingyuanlinkJavaApplication.java    # 启动类
├── common/                             # 公共模块
│   ├── config/                        # 配置类
│   ├── constant/                      # 常量定义
│   ├── controller/                    # 系统管理控制器
│   ├── exception/                     # 异常处理
│   ├── service/                       # 公共服务
│   ├── task/                          # 定时任务
│   └── utils/                         # 工具类
└── moudel/                            # 业务模块
    ├── user/                          # 用户管理
    ├── device/                        # 设备管理
    ├── income/                        # 收益管理
    └── withdraw/                      # 提现管理
```

## 快速开始

### 1. 环境要求

- JDK 17+
- MySQL 8.0+
- Redis 6.0+
- Maven 3.6+

### 2. 数据库初始化

```sql
-- 执行数据库初始化脚本
source src/main/resources/sql/init.sql;
source src/main/resources/sql/data.sql;
```

### 3. 配置文件

修改 `src/main/resources/application.yml` 中的数据库和Redis连接信息：

```yaml
spring:
  datasource:
    url: ****************************************
    username: your_username
    password: your_password
  data:
    redis:
      host: localhost
      port: 6379
      password: your_redis_password
```

### 4. 启动应用

```bash
mvn spring-boot:run
```

应用启动后访问：
- API文档: http://localhost:8080/api/swagger-ui/index.html
- 系统监控: http://localhost:8080/api/system/health

## API接口

### 用户管理
- `POST /api/user/sms/send` - 发送短信验证码
- `POST /api/user/register` - 用户注册
- `POST /api/user/login` - 用户登录
- `GET /api/user/info` - 获取用户信息
- `PUT /api/user/info/update` - 更新用户信息

### 设备管理
- `GET /api/user/devices` - 获取设备列表
- `POST /api/user/devices/bind` - 绑定设备
- `POST /api/user/devices/{sn}/unbind` - 解绑设备
- `GET /api/user/devices/{sn}/detail` - 获取设备详情

### 收益管理
- `GET /api/user/income/summary` - 获取收益概览
- `GET /api/user/income/detail` - 获取收益明细
- `GET /api/user/income/chart` - 获取收益趋势图

### 提现管理
- `GET /api/user/withdraw/summary` - 获取提现概览
- `POST /api/user/withdraw/apply` - 申请提现
- `GET /api/user/withdraw/records` - 获取提现记录

### 系统管理
- `GET /api/system/health` - 系统健康检查
- `POST /api/system/sync/devices` - 同步设备状态
- `POST /api/system/sync/income` - 同步收益数据

## 定时任务

系统包含以下定时任务：

- **设备状态同步**: 每30分钟同步一次设备状态
- **收益数据同步**: 每日凌晨2点同步前一天的收益数据
- **数据清理**: 每日凌晨3点清理过期数据
- **系统监控**: 每5分钟进行一次系统健康检查

## 配置说明

### 业务配置

```yaml
business:
  user:
    max-device-count: 10          # 用户最大设备绑定数
    default-commission-rate: 0.1000  # 默认抽成比例
  withdraw:
    min-amount: 10.00             # 最小提现金额
    max-amount: 10000.00          # 最大提现金额
    daily-limit: 50000.00         # 每日提现限额
  sms-code:
    length: 6                     # 验证码长度
    expire-minutes: 5             # 过期时间(分钟)
    daily-limit: 10               # 每日发送限制
```

### 上游API配置

```yaml
upstream:
  api:
    base-url: https://gw.fudiancloud.com
    timeout: 30000
    retry-count: 3
```

## 部署说明

### Docker部署

```dockerfile
FROM openjdk:17-jdk-slim
COPY target/lingyuanlink-java-*.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

### 生产环境配置

1. 修改数据库连接池配置
2. 配置Redis集群
3. 设置日志级别为INFO
4. 配置文件加密
5. 启用HTTPS

## 注意事项

1. 本项目为小程序后端，不包含管理端功能
2. 短信服务需要配置真实的短信服务商
3. 上游API需要根据实际情况调整
4. 生产环境请修改默认密码和密钥
5. 建议使用专业的监控工具监控系统状态

## 许可证

本项目采用 MIT 许可证。
