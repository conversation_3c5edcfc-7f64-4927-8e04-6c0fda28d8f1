package com.lylink.common.utils;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 短信验证码工具类
 */
public class SmsCodeUtils {

    private static final SecureRandom random = new SecureRandom();

    /**
     * 生成数字验证码
     * 
     * @param length 验证码长度
     * @return 验证码
     */
    public static String generateNumericCode(int length) {
        if (length <= 0) {
            length = 6;
        }
        
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < length; i++) {
            code.append(random.nextInt(10));
        }
        
        return code.toString();
    }

    /**
     * 生成字母数字混合验证码
     * 
     * @param length 验证码长度
     * @return 验证码
     */
    public static String generateAlphanumericCode(int length) {
        if (length <= 0) {
            length = 6;
        }
        
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        StringBuilder code = new StringBuilder();
        
        for (int i = 0; i < length; i++) {
            code.append(chars.charAt(random.nextInt(chars.length())));
        }
        
        return code.toString();
    }

    /**
     * 生成默认6位数字验证码
     * 
     * @return 6位数字验证码
     */
    public static String generateCode() {
        return generateNumericCode(6);
    }

    /**
     * 验证验证码格式
     * 
     * @param code 验证码
     * @param length 期望长度
     * @return true-格式正确，false-格式错误
     */
    public static boolean isValidCodeFormat(String code, int length) {
        if (StringUtils.isEmpty(code)) {
            return false;
        }
        
        if (code.length() != length) {
            return false;
        }
        
        // 检查是否为纯数字
        return code.matches("^\\d+$");
    }

    /**
     * 验证默认6位数字验证码格式
     * 
     * @param code 验证码
     * @return true-格式正确，false-格式错误
     */
    public static boolean isValidCode(String code) {
        return isValidCodeFormat(code, 6);
    }

    /**
     * 生成验证码缓存键
     * 
     * @param phone 手机号
     * @param type 验证码类型
     * @return 缓存键
     */
    public static String generateCacheKey(String phone, String type) {
        return String.format("sms_code:%s:%s", type, phone);
    }

    /**
     * 生成验证码发送频率限制缓存键
     * 
     * @param phone 手机号
     * @return 缓存键
     */
    public static String generateRateLimitKey(String phone) {
        return String.format("sms_rate_limit:%s", phone);
    }

    /**
     * 生成每日发送次数限制缓存键
     * 
     * @param phone 手机号
     * @return 缓存键
     */
    public static String generateDailyLimitKey(String phone) {
        String today = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        return String.format("sms_daily_limit:%s:%s", today, phone);
    }

    /**
     * 验证码类型枚举
     */
    public enum CodeType {
        REGISTER("register", "注册"),
        LOGIN("login", "登录"),
        RESET_PASSWORD("reset_password", "重置密码"),
        BIND_DEVICE("bind_device", "绑定设备"),
        WITHDRAW("withdraw", "提现");

        private final String code;
        private final String description;

        CodeType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static CodeType fromCode(String code) {
            for (CodeType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }

    /**
     * 获取验证码模板内容
     * 
     * @param code 验证码
     * @param type 验证码类型
     * @param expireMinutes 过期时间（分钟）
     * @return 模板内容
     */
    public static String getTemplateContent(String code, CodeType type, int expireMinutes) {
        switch (type) {
            case REGISTER:
                return String.format("您的注册验证码是：%s，%d分钟内有效，请勿泄露给他人。", code, expireMinutes);
            case LOGIN:
                return String.format("您的登录验证码是：%s，%d分钟内有效，请勿泄露给他人。", code, expireMinutes);
            case RESET_PASSWORD:
                return String.format("您的密码重置验证码是：%s，%d分钟内有效，请勿泄露给他人。", code, expireMinutes);
            case BIND_DEVICE:
                return String.format("您的设备绑定验证码是：%s，%d分钟内有效，请勿泄露给他人。", code, expireMinutes);
            case WITHDRAW:
                return String.format("您的提现验证码是：%s，%d分钟内有效，请勿泄露给他人。", code, expireMinutes);
            default:
                return String.format("您的验证码是：%s，%d分钟内有效，请勿泄露给他人。", code, expireMinutes);
        }
    }

    /**
     * 检查验证码是否过期
     * 
     * @param createTime 创建时间
     * @param expireMinutes 过期时间（分钟）
     * @return true-已过期，false-未过期
     */
    public static boolean isExpired(LocalDateTime createTime, int expireMinutes) {
        if (createTime == null) {
            return true;
        }
        
        LocalDateTime expireTime = createTime.plusMinutes(expireMinutes);
        return LocalDateTime.now().isAfter(expireTime);
    }

    /**
     * 计算验证码剩余有效时间（秒）
     * 
     * @param createTime 创建时间
     * @param expireMinutes 过期时间（分钟）
     * @return 剩余有效时间（秒），如果已过期返回0
     */
    public static long getRemainingSeconds(LocalDateTime createTime, int expireMinutes) {
        if (createTime == null) {
            return 0;
        }
        
        LocalDateTime expireTime = createTime.plusMinutes(expireMinutes);
        LocalDateTime now = LocalDateTime.now();
        
        if (now.isAfter(expireTime)) {
            return 0;
        }
        
        return java.time.Duration.between(now, expireTime).getSeconds();
    }
}
