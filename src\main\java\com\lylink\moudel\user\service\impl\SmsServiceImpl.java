package com.lylink.moudel.user.service.impl;

import com.lylink.common.config.BusinessConfig;
import com.lylink.common.config.SmsConfig;
import com.lylink.common.utils.RedisUtils;
import com.lylink.common.utils.SmsCodeUtils;
import com.lylink.moudel.user.mapper.SmsCodeMapper;
import com.lylink.moudel.user.service.SmsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

/**
 * 短信服务实现类
 */
@Slf4j
@Service
public class SmsServiceImpl implements SmsService {

    @Autowired
    private SmsConfig smsConfig;

    @Autowired
    private BusinessConfig businessConfig;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private SmsCodeMapper smsCodeMapper;

    /**
     * 发送频率限制时间（秒）
     */
    private static final int RATE_LIMIT_SECONDS = 60;

    @Override
    public boolean sendSmsCode(String phone, String code, SmsCodeUtils.CodeType type) {
        try {
            // 检查是否为模拟模式
            if (smsConfig.isMockMode()) {
                log.info("模拟发送短信验证码: phone={}, code={}, type={}", phone, code, type.getDescription());
                return true;
            }

            // 根据配置的服务商发送短信
            SmsConfig.Provider provider = smsConfig.getProviderEnum();
            switch (provider) {
                case ALIYUN:
                    return sendByAliyun(phone, code, type);
                case TENCENT:
                    return sendByTencent(phone, code, type);
                case HUAWEI:
                    return sendByHuawei(phone, code, type);
                default:
                    log.warn("未知的短信服务商: {}", provider);
                    return false;
            }
        } catch (Exception e) {
            log.error("发送短信验证码失败: phone={}, type={}", phone, type, e);
            return false;
        } finally {
            // 设置发送频率限制
            setRateLimit(phone);
            // 增加每日发送次数
            increaseDailyCount(phone);
        }
    }

    @Override
    public boolean sendSmsWithTemplate(String phone, String templateCode, String... params) {
        try {
            if (smsConfig.isMockMode()) {
                log.info("模拟发送短信: phone={}, template={}, params={}", phone, templateCode, params);
                return true;
            }

            // 实际发送逻辑
            // 这里可以根据不同的服务商实现具体的发送逻辑
            log.info("发送短信: phone={}, template={}", phone, templateCode);
            return true;
        } catch (Exception e) {
            log.error("发送短信失败: phone={}, template={}", phone, templateCode, e);
            return false;
        }
    }

    @Override
    public boolean checkRateLimit(String phone) {
        String key = SmsCodeUtils.generateRateLimitKey(phone);
        return !redisUtils.hasKey(key);
    }

    @Override
    public boolean checkDailyLimit(String phone) {
        String key = SmsCodeUtils.generateDailyLimitKey(phone);
        Object count = redisUtils.get(key);
        int dailyCount = count != null ? (Integer) count : 0;
        return dailyCount < businessConfig.getSmsCode().getDailyLimit();
    }

    @Override
    public long getRemainingCooldown(String phone) {
        String key = SmsCodeUtils.generateRateLimitKey(phone);
        Long ttl = redisUtils.getExpire(key);
        return ttl != null && ttl > 0 ? ttl : 0;
    }

    /**
     * 设置发送频率限制
     * 
     * @param phone 手机号
     */
    private void setRateLimit(String phone) {
        String key = SmsCodeUtils.generateRateLimitKey(phone);
        redisUtils.set(key, "1", RATE_LIMIT_SECONDS);
    }

    /**
     * 增加每日发送次数
     * 
     * @param phone 手机号
     */
    private void increaseDailyCount(String phone) {
        String key = SmsCodeUtils.generateDailyLimitKey(phone);
        Object count = redisUtils.get(key);
        int dailyCount = count != null ? (Integer) count : 0;
        redisUtils.set(key, dailyCount + 1, 24 * 60 * 60); // 24小时过期
    }

    /**
     * 通过阿里云发送短信
     * 
     * @param phone 手机号
     * @param code 验证码
     * @param type 验证码类型
     * @return 发送结果
     */
    private boolean sendByAliyun(String phone, String code, SmsCodeUtils.CodeType type) {
        try {
            // 这里实现阿里云短信发送逻辑
            // 需要引入阿里云短信SDK
            log.info("通过阿里云发送短信: phone={}, code={}, type={}", phone, code, type.getDescription());
            
            String templateCode = smsConfig.getTemplateCode().getTemplateByType(type.getCode());
            String content = SmsCodeUtils.getTemplateContent(code, type, businessConfig.getSmsCode().getExpireMinutes());
            
            // 实际的阿里云短信发送代码
            // DefaultProfile profile = DefaultProfile.getProfile("cn-hangzhou", smsConfig.getAccessKey(), smsConfig.getAccessSecret());
            // IAcsClient client = new DefaultAcsClient(profile);
            // SendSmsRequest request = new SendSmsRequest();
            // request.setPhoneNumbers(phone);
            // request.setSignName(smsConfig.getSignName());
            // request.setTemplateCode(templateCode);
            // request.setTemplateParam("{\"code\":\"" + code + "\"}");
            // SendSmsResponse response = client.getAcsResponse(request);
            // return "OK".equals(response.getCode());
            
            // 模拟发送成功
            return true;
        } catch (Exception e) {
            log.error("阿里云短信发送失败: phone={}", phone, e);
            return false;
        }
    }

    /**
     * 通过腾讯云发送短信
     * 
     * @param phone 手机号
     * @param code 验证码
     * @param type 验证码类型
     * @return 发送结果
     */
    private boolean sendByTencent(String phone, String code, SmsCodeUtils.CodeType type) {
        try {
            // 这里实现腾讯云短信发送逻辑
            log.info("通过腾讯云发送短信: phone={}, code={}, type={}", phone, code, type.getDescription());
            
            // 实际的腾讯云短信发送代码
            // 模拟发送成功
            return true;
        } catch (Exception e) {
            log.error("腾讯云短信发送失败: phone={}", phone, e);
            return false;
        }
    }

    /**
     * 通过华为云发送短信
     * 
     * @param phone 手机号
     * @param code 验证码
     * @param type 验证码类型
     * @return 发送结果
     */
    private boolean sendByHuawei(String phone, String code, SmsCodeUtils.CodeType type) {
        try {
            // 这里实现华为云短信发送逻辑
            log.info("通过华为云发送短信: phone={}, code={}, type={}", phone, code, type.getDescription());
            
            // 实际的华为云短信发送代码
            // 模拟发送成功
            return true;
        } catch (Exception e) {
            log.error("华为云短信发送失败: phone={}", phone, e);
            return false;
        }
    }
}
