package com.lylink.common.constant;

/**
 * 业务常量类
 */
public class BusinessConstants {

    /**
     * 用户状态
     */
    public static class UserStatus {
        public static final Integer NORMAL = 1;    // 正常
        public static final Integer DISABLED = 0;  // 禁用
    }

    /**
     * 设备状态
     */
    public static class DeviceStatus {
        public static final Integer ONLINE = 1;    // 在线
        public static final Integer OFFLINE = 0;   // 离线
        public static final Integer ERROR = -1;    // 异常
    }

    /**
     * 提现状态
     */
    public static class WithdrawStatus {
        public static final String PENDING = "pending";       // 待审核
        public static final String APPROVED = "approved";     // 已通过
        public static final String REJECTED = "rejected";     // 已拒绝
        public static final String PROCESSING = "processing"; // 处理中
        public static final String COMPLETED = "completed";   // 已完成
    }

    /**
     * 验证码类型
     */
    public static class SmsCodeType {
        public static final String REGISTER = "register";           // 注册
        public static final String LOGIN = "login";                 // 登录
        public static final String RESET_PASSWORD = "reset_password"; // 重置密码
        public static final String BIND_DEVICE = "bind_device";     // 绑定设备
        public static final String WITHDRAW = "withdraw";           // 提现
    }

    /**
     * 收益同步状态
     */
    public static class IncomeSyncStatus {
        public static final Integer NOT_SYNCED = 0;  // 未同步
        public static final Integer SYNCED = 1;      // 已同步
    }

    /**
     * 操作日志类型
     */
    public static class OperationType {
        public static final String USER_REGISTER = "user_register";     // 用户注册
        public static final String USER_LOGIN = "user_login";           // 用户登录
        public static final String USER_UPDATE = "user_update";         // 用户信息更新
        public static final String DEVICE_BIND = "device_bind";         // 设备绑定
        public static final String DEVICE_UNBIND = "device_unbind";     // 设备解绑
        public static final String WITHDRAW_APPLY = "withdraw_apply";   // 提现申请
        public static final String WITHDRAW_AUDIT = "withdraw_audit";   // 提现审核
        public static final String SMS_SEND = "sms_send";               // 短信发送
        public static final String DATA_SYNC = "data_sync";             // 数据同步
    }

    /**
     * 用户类型
     */
    public static class UserType {
        public static final String USER = "user";   // 普通用户
        public static final String ADMIN = "admin"; // 管理员
    }

    /**
     * 操作状态
     */
    public static class OperationStatus {
        public static final Integer SUCCESS = 1;  // 成功
        public static final Integer FAILED = 0;   // 失败
    }

    /**
     * 缓存键前缀
     */
    public static class CachePrefix {
        public static final String SMS_CODE = "sms_code:";              // 短信验证码
        public static final String SMS_RATE_LIMIT = "sms_rate_limit:";  // 短信发送频率限制
        public static final String SMS_DAILY_LIMIT = "sms_daily_limit:"; // 短信每日发送限制
        public static final String USER_TOKEN = "user_token:";          // 用户token
        public static final String DEVICE_STATUS = "device_status:";    // 设备状态
        public static final String USER_INCOME = "user_income:";        // 用户收益
        public static final String SYSTEM_CONFIG = "system_config:";    // 系统配置
    }

    /**
     * 默认值
     */
    public static class DefaultValues {
        public static final String DEFAULT_NICKNAME = "用户";           // 默认昵称
        public static final String DEFAULT_SETTLEMENT_CYCLE = "monthly"; // 默认结算周期
        public static final Integer DEFAULT_SMS_CODE_LENGTH = 6;        // 默认验证码长度
        public static final Integer DEFAULT_SMS_CODE_EXPIRE = 5;        // 默认验证码过期时间(分钟)
        public static final Integer DEFAULT_SMS_DAILY_LIMIT = 10;       // 默认每日短信发送限制
        public static final Integer DEFAULT_DEVICE_SYNC_INTERVAL = 30;  // 默认设备同步间隔(分钟)
        public static final Integer DEFAULT_DEVICE_OFFLINE_THRESHOLD = 60; // 默认设备离线阈值(分钟)
    }

    /**
     * 时间格式
     */
    public static class DateFormat {
        public static final String DATETIME = "yyyy-MM-dd HH:mm:ss";
        public static final String DATE = "yyyy-MM-dd";
        public static final String TIME = "HH:mm:ss";
        public static final String MONTH = "yyyy-MM";
        public static final String YEAR = "yyyy";
        public static final String TIMESTAMP = "yyyyMMddHHmmss";
    }

    /**
     * 正则表达式
     */
    public static class RegexPattern {
        public static final String PHONE = "^1[3-9]\\d{9}$";                    // 手机号
        public static final String PASSWORD = "^.{6,}$";                        // 密码(至少6位)
        public static final String STRONG_PASSWORD = "^(?=.*[A-Za-z])(?=.*\\d)[A-Za-z\\d@$!%*?&]{6,}$"; // 强密码
        public static final String SMS_CODE = "^\\d{6}$";                       // 6位数字验证码
        public static final String DEVICE_SN = "^[A-Za-z0-9]{6,20}$";          // 设备序列号
        public static final String ALIPAY_ACCOUNT = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}|^1[3-9]\\d{9}$"; // 支付宝账号(邮箱或手机号)
    }

    /**
     * 响应消息
     */
    public static class ResponseMessage {
        public static final String SUCCESS = "操作成功";
        public static final String FAILED = "操作失败";
        public static final String INVALID_PARAM = "参数错误";
        public static final String UNAUTHORIZED = "未授权访问";
        public static final String FORBIDDEN = "权限不足";
        public static final String NOT_FOUND = "资源不存在";
        public static final String INTERNAL_ERROR = "系统内部错误";
        public static final String SMS_SEND_SUCCESS = "验证码发送成功";
        public static final String SMS_SEND_FAILED = "验证码发送失败";
        public static final String LOGIN_SUCCESS = "登录成功";
        public static final String LOGIN_FAILED = "登录失败";
        public static final String REGISTER_SUCCESS = "注册成功";
        public static final String REGISTER_FAILED = "注册失败";
    }

    /**
     * 数值限制
     */
    public static class Limits {
        public static final int MAX_DEVICE_ALIAS_LENGTH = 50;      // 设备别名最大长度
        public static final int MAX_NICKNAME_LENGTH = 50;          // 昵称最大长度
        public static final int MAX_ALIPAY_NAME_LENGTH = 50;       // 支付宝姓名最大长度
        public static final int MAX_WITHDRAW_REASON_LENGTH = 500;  // 提现原因最大长度
        public static final int MIN_PASSWORD_LENGTH = 6;           // 密码最小长度
        public static final int MAX_PASSWORD_LENGTH = 20;          // 密码最大长度
    }
}
