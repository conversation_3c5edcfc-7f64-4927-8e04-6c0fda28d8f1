package com.lylink.moudel.user.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 短信验证码DTO
 */
@Data
public class SmsCodeDTO {

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    /**
     * 验证码类型：register-注册，login-登录，reset_password-重置密码
     */
    @NotBlank(message = "验证码类型不能为空")
    @Pattern(regexp = "^(register|login|reset_password)$", 
             message = "验证码类型只能是register、login或reset_password")
    private String type;
}
