package com.lylink.moudel.user.pojo;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("users")
public class User implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 手机号
     */
    @TableField("phone")
    private String phone;

    /**
     * 密码哈希
     */
    @TableField("password_hash")
    private String passwordHash;

    /**
     * 昵称
     */
    @TableField("nickname")
    private String nickname;

    /**
     * 支付宝账号
     */
    @TableField("alipay_account")
    private String alipayAccount;

    /**
     * 支付宝姓名
     */
    @TableField("alipay_name")
    private String alipayName;

    /**
     * 抽成比例
     */
    @TableField("commission_rate")
    private BigDecimal commissionRate;

    /**
     * 结算周期
     */
    @TableField("settlement_cycle")
    private String settlementCycle;

    /**
     * 状态：1-正常，0-禁用
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}
