package com.lylink.moudel.withdraw.pojo;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 提现申请实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("withdraw_applications")
public class WithdrawApplication implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 申请ID(UUID)
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 提现金额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 支付宝账号
     */
    @TableField("alipay_account")
    private String alipayAccount;

    /**
     * 支付宝姓名
     */
    @TableField("alipay_name")
    private String alipayName;

    /**
     * 状态：pending-待审核，approved-已通过，rejected-已拒绝，processing-处理中，completed-已完成
     */
    @TableField("status")
    private String status;

    /**
     * 审核原因
     */
    @TableField("audit_reason")
    private String auditReason;

    /**
     * 审核管理员ID
     */
    @TableField("audit_admin_id")
    private Long auditAdminId;

    /**
     * 申请时间
     */
    @TableField(value = "apply_time", fill = FieldFill.INSERT)
    private LocalDateTime applyTime;

    /**
     * 审核时间
     */
    @TableField("audit_time")
    private LocalDateTime auditTime;

    /**
     * 处理时间
     */
    @TableField("process_time")
    private LocalDateTime processTime;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;
}
