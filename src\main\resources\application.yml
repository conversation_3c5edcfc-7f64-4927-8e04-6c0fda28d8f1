spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************************
    username: root
    password: 121314
    # druid ?????
    druid:
      filter:
        config:
          #??????
          enabled: true
        stat:
          enabled: true
      # ????????????Filter
      # ??????????SQL????
      # stat - SQL????
      # wall - SQL?????
      # slf4j - Druid????
      filters: stat,wall,slf4j
      # ????????
      initial-size: 5
      # ????????
      max-active: 20
      # ?????PSCache????
      # ????0?pool-prepared-statements????
      max-pool-prepared-statement-per-connection-size: -1
      # ????????????????
      max-wait: 60000
      # ????????????????????????
      min-evictable-idle-time-millis: 300000
      # ????????
      min-idle: 5
      # ????PSCache??????preparedStatement???????????
      # ?????????????????Oracle
      pool-prepared-statements: false
      # ???????????
      # ????????
      test-on-borrow: false
      # ???????????
      # ????????
      test-on-return: false
      # ??????
      # ??????????
      test-while-idle: true
      # ????????????????????
      time-between-eviction-runs-millis: 60000
      # ???????SQL
      # ???test-while-idle?test-on-borrow?test-on-return????
      validation-query: SELECT 1
      # ?????????????
      validation-query-timeout: 1
      stat-view-servlet:
        # ?????
        allow: 127.0.0.1
        # ??????
        enabled: true
        # ????
        login-password: 123456
        # ?????
        login-username: root
        # ????????
        reset-enable: true
      web-stat-filter:
        # ????????
        enabled: true
        # ????
        exclusions: .js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,/druid/*
        # ??session??
        session-stat-enable: true
        # session???????
        session-stat-max-count: 100
        # ????
        url-pattern: /*

mybatis-plus:
  global-config:
    db-config:
      # ???????
      id-type: auto
  # mapper xml????
  mapper-locations: classpath:mapper/*.xml

# Logger Config
logging:
  level:
    com.hexadecimal: debug

springdoc:
  api-docs:
    enabled: true # ??OpenApi??
    path: /user-service/v3/api-docs  # ????????? "/v3/api-docs"
  swagger-ui:
    enabled: true # ??swagger?????OpenApi???OpenApi????
    path: /user-service/swagger-ui/index.html # ?????????"/swagger-ui/index.html"

# Minio??
minio:
  endpoint: http://122.51.232.140:9000
  accessKey: minioadmin
  secretKey: minioadmin
  bucketName: guandan

#???? swagger-ui
swagger:
  enabled: true
#??jwt???
jwt:
  signKey: admin
  expirationTime: 7