spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************************
    username: root
    password: 121314
    # druid 连接池配置
    druid:
      filter:
        config:
          # 开启配置
          enabled: true
        stat:
          enabled: true
      # 配置监控统计拦截的Filter
      # 去掉后监控界面SQL无法统计
      # stat - SQL监控配置
      # wall - SQL防火墙配置
      # slf4j - Druid日志配置
      filters: stat,wall,slf4j
      # 初始连接数
      initial-size: 5
      # 最大连接数
      max-active: 20
      # 要启用PSCache，必须配置大于0
      # 当大于0时pool-prepared-statements自动触发修改为true
      max-pool-prepared-statement-per-connection-size: -1
      # 配置获取连接等待超时的时间
      max-wait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接
      min-evictable-idle-time-millis: 300000
      # 最小连接数
      min-idle: 5
      # 是否缓存preparedStatement，也就是PSCache
      # PSCache对支持游标的数据库性能提升巨大，比如说oracle
      pool-prepared-statements: false
      # 申请连接时执行validationQuery检测连接是否有效
      # 做了这个配置会降低性能
      test-on-borrow: false
      # 归还连接时执行validationQuery检测连接是否有效
      # 做了这个配置会降低性能
      test-on-return: false
      # 建议配置为true
      # 不影响性能，并且保证安全性
      test-while-idle: true
      # 配置间隔多久才进行一次检测
      time-between-eviction-runs-millis: 60000
      # 用来检测连接是否有效的SQL
      # 要求是一个查询语句test-while-idle,test-on-borrow,test-on-return任何一个为true时生效
      validation-query: SELECT 1
      # 单位：秒，检测连接是否有效的超时时间
      validation-query-timeout: 1
      stat-view-servlet:
        # 白名单
        allow: 127.0.0.1
        # 是否启用
        enabled: true
        # 密码
        login-password: 123456
        # 用户名
        login-username: root
        # 是否能够重置数据
        reset-enable: true
      web-stat-filter:
        # 是否启用
        enabled: true
        # 排除
        exclusions: .js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,/druid/*
        # 是否开启session统计
        session-stat-enable: true
        # session统计的最大个数
        session-stat-max-count: 100
        # 匹配
        url-pattern: /*

mybatis-plus:
  global-config:
    db-config:
      # 主键类型
      id-type: auto
  # mapper xml文件位置
  mapper-locations: classpath:mapper/*.xml

# Logger Config
logging:
  level:
    com.lylink: debug

springdoc:
  api-docs:
    enabled: true # 开启OpenApi文档
    path: /api/v3/api-docs  # 默认路径为 "/v3/api-docs"
  swagger-ui:
    enabled: true # 开启swagger文档界面，OpenApi文档开启后OpenApi文档才会生效
    path: /api/swagger-ui/index.html # 默认路径为"/swagger-ui/index.html"

# Minio配置
minio:
  endpoint: http://**************:9000
  accessKey: minioadmin
  secretKey: minioadmin
  bucketName: lingyuanlink

# 开启 swagger-ui
swagger:
  enabled: true

# JWT配置
jwt:
  signKey: lingyuanlink_jwt_secret_2024
  expirationTime: 168  # 7天，单位小时

# Redis配置
spring:
  data:
    redis:
      host: 127.0.0.1
      port: 6379
      password:
      database: 0
      timeout: 3000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1ms

# 上游API配置
upstream:
  api:
    base-url: https://gw.fudiancloud.com
    timeout: 30000  # 30秒
    retry-count: 3

# 短信配置
sms:
  provider: aliyun  # 短信服务商
  access-key: your_access_key
  access-secret: your_access_secret
  sign-name: 灵源Link
  template-code:
    register: SMS_123456789  # 注册验证码模板
    login: SMS_123456790     # 登录验证码模板
    reset: SMS_123456791     # 重置密码模板

# 业务配置
business:
  # 用户配置
  user:
    max-device-count: 10  # 用户最大设备绑定数
    default-commission-rate: 0.1000  # 默认抽成比例

  # 提现配置
  withdraw:
    min-amount: 10.00  # 最小提现金额
    max-amount: 10000.00  # 最大提现金额
    daily-limit: 50000.00  # 每日提现限额
    fee-rate: 0.0000  # 提现手续费率

  # 验证码配置
  sms-code:
    length: 6  # 验证码长度
    expire-minutes: 5  # 过期时间(分钟)
    daily-limit: 10  # 每日发送限制

  # 设备配置
  device:
    sync-interval: 30  # 同步间隔(分钟)
    offline-threshold: 60  # 离线判定阈值(分钟)