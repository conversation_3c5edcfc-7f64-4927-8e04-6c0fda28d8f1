package com.lylink.moudel.device.service;

import com.lylink.moudel.device.vo.DeviceBenchmarkVO;
import com.lylink.moudel.device.vo.DeviceInfoVO;
import com.lylink.moudel.device.vo.DeviceTrafficVO;

import java.util.List;

/**
 * 上游API服务接口
 */
public interface UpstreamApiService {

    /**
     * 获取设备列表
     * 
     * @return 设备列表
     */
    List<DeviceInfoVO> getDevices();

    /**
     * 获取设备详情
     * 
     * @param sn 设备序列号
     * @return 设备详情
     */
    DeviceInfoVO getDeviceDetail(String sn);

    /**
     * 获取设备流量统计
     * 
     * @param sn 设备序列号
     * @param days 统计天数
     * @return 流量统计
     */
    DeviceTrafficVO getDeviceTraffic(String sn, int days);

    /**
     * 获取设备性能测试结果
     * 
     * @param sn 设备序列号
     * @return 性能测试结果
     */
    DeviceBenchmarkVO getDeviceBenchmark(String sn);

    /**
     * 启动设备性能测试
     * 
     * @param sn 设备序列号
     * @return 启动结果
     */
    boolean startDeviceBenchmark(String sn);

    /**
     * 检查设备是否存在
     * 
     * @param sn 设备序列号
     * @return 是否存在
     */
    boolean checkDeviceExists(String sn);

    /**
     * 获取设备网卡状态
     * 
     * @param sn 设备序列号
     * @return 网卡状态
     */
    Object getDeviceNetworkStatus(String sn);

    /**
     * 同步设备状态
     * 
     * @param sn 设备序列号
     * @return 同步结果
     */
    boolean syncDeviceStatus(String sn);

    /**
     * 批量同步设备状态
     * 
     * @param snList 设备序列号列表
     * @return 同步结果
     */
    boolean batchSyncDeviceStatus(List<String> snList);
}
