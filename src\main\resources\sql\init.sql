-- 灵源Link设备管理与收益分发平台数据库初始化脚本
-- 创建数据库
CREATE DATABASE IF NOT EXISTS `lingyuanlink` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `lingyuanlink`;

-- 1. 用户表 (users)
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `phone` VARCHAR(11) NOT NULL COMMENT '手机号',
  `password_hash` VARCHAR(255) NOT NULL COMMENT '密码哈希',
  `nickname` VARCHAR(50) DEFAULT NULL COMMENT '昵称',
  `alipay_account` VARCHAR(100) DEFAULT NULL COMMENT '支付宝账号',
  `alipay_name` VARCHAR(50) DEFAULT NULL COMMENT '支付宝姓名',
  `commission_rate` DECIMAL(5,4) DEFAULT 0.1000 COMMENT '抽成比例',
  `settlement_cycle` VARCHAR(20) DEFAULT 'monthly' COMMENT '结算周期',
  `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_phone` (`phone`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 2. 管理员表 (admins)
DROP TABLE IF EXISTS `admins`;
CREATE TABLE `admins` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '管理员ID',
  `username` VARCHAR(50) NOT NULL COMMENT '用户名',
  `password_hash` VARCHAR(255) NOT NULL COMMENT '密码哈希',
  `real_name` VARCHAR(50) DEFAULT NULL COMMENT '真实姓名',
  `role` VARCHAR(20) DEFAULT 'admin' COMMENT '角色：admin-管理员，super-超级管理员',
  `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-禁用',
  `last_login_time` TIMESTAMP NULL COMMENT '最后登录时间',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员表';

-- 3. 设备表 (devices)
DROP TABLE IF EXISTS `devices`;
CREATE TABLE `devices` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '设备ID',
  `sn` VARCHAR(100) NOT NULL COMMENT '设备序列号',
  `alias` VARCHAR(100) DEFAULT NULL COMMENT '设备别名',
  `user_id` BIGINT DEFAULT NULL COMMENT '绑定用户ID',
  `bind_time` TIMESTAMP NULL COMMENT '绑定时间',
  `status` TINYINT DEFAULT 1 COMMENT '状态：1-正常，0-离线，-1-异常',
  `device_type` VARCHAR(50) DEFAULT NULL COMMENT '设备类型',
  `hardware_info` JSON DEFAULT NULL COMMENT '硬件信息',
  `last_sync_time` TIMESTAMP NULL COMMENT '最后同步时间',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_sn` (`sn`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_bind_time` (`bind_time`),
  CONSTRAINT `fk_devices_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='设备表';

-- 4. 收益记录表 (income_records)
DROP TABLE IF EXISTS `income_records`;
CREATE TABLE `income_records` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `device_sn` VARCHAR(100) NOT NULL COMMENT '设备序列号',
  `date` DATE NOT NULL COMMENT '收益日期',
  `upstream_income` DECIMAL(10,4) NOT NULL DEFAULT 0.0000 COMMENT '上游收益',
  `commission` DECIMAL(10,4) NOT NULL DEFAULT 0.0000 COMMENT '平台抽成',
  `actual_income` DECIMAL(10,4) NOT NULL DEFAULT 0.0000 COMMENT '实际收益',
  `traffic_mb` BIGINT DEFAULT 0 COMMENT '流量消耗(MB)',
  `online_duration` INT DEFAULT 0 COMMENT '在线时长(分钟)',
  `sync_status` TINYINT DEFAULT 0 COMMENT '同步状态：0-未同步，1-已同步',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_device_date` (`user_id`, `device_sn`, `date`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_device_sn` (`device_sn`),
  KEY `idx_date` (`date`),
  KEY `idx_sync_status` (`sync_status`),
  CONSTRAINT `fk_income_records_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='收益记录表';

-- 5. 提现申请表 (withdraw_applications)
DROP TABLE IF EXISTS `withdraw_applications`;
CREATE TABLE `withdraw_applications` (
  `id` VARCHAR(36) NOT NULL COMMENT '申请ID(UUID)',
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `amount` DECIMAL(10,2) NOT NULL COMMENT '提现金额',
  `alipay_account` VARCHAR(100) NOT NULL COMMENT '支付宝账号',
  `alipay_name` VARCHAR(50) NOT NULL COMMENT '支付宝姓名',
  `status` ENUM('pending', 'approved', 'rejected', 'processing', 'completed') DEFAULT 'pending' COMMENT '状态',
  `audit_reason` TEXT DEFAULT NULL COMMENT '审核原因',
  `audit_admin_id` BIGINT DEFAULT NULL COMMENT '审核管理员ID',
  `apply_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
  `audit_time` TIMESTAMP NULL COMMENT '审核时间',
  `process_time` TIMESTAMP NULL COMMENT '处理时间',
  `remark` TEXT DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_apply_time` (`apply_time`),
  KEY `idx_audit_admin_id` (`audit_admin_id`),
  CONSTRAINT `fk_withdraw_applications_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_withdraw_applications_audit_admin_id` FOREIGN KEY (`audit_admin_id`) REFERENCES `admins` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='提现申请表';

-- 6. 系统配置表 (system_config)
DROP TABLE IF EXISTS `system_config`;
CREATE TABLE `system_config` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` VARCHAR(100) NOT NULL COMMENT '配置键',
  `config_value` TEXT NOT NULL COMMENT '配置值',
  `description` VARCHAR(255) DEFAULT NULL COMMENT '配置描述',
  `config_type` VARCHAR(20) DEFAULT 'string' COMMENT '配置类型：string,number,boolean,json',
  `is_public` TINYINT DEFAULT 0 COMMENT '是否公开：1-公开，0-私有',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`),
  KEY `idx_is_public` (`is_public`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 7. 操作日志表 (operation_logs)
DROP TABLE IF EXISTS `operation_logs`;
CREATE TABLE `operation_logs` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_type` ENUM('user', 'admin') NOT NULL COMMENT '用户类型',
  `user_id` BIGINT NOT NULL COMMENT '用户ID',
  `operation` VARCHAR(100) NOT NULL COMMENT '操作类型',
  `description` TEXT DEFAULT NULL COMMENT '操作描述',
  `ip_address` VARCHAR(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` TEXT DEFAULT NULL COMMENT '用户代理',
  `request_data` JSON DEFAULT NULL COMMENT '请求数据',
  `response_data` JSON DEFAULT NULL COMMENT '响应数据',
  `status` TINYINT DEFAULT 1 COMMENT '状态：1-成功，0-失败',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_type_user_id` (`user_type`, `user_id`),
  KEY `idx_operation` (`operation`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';

-- 8. 短信验证码表 (sms_codes)
DROP TABLE IF EXISTS `sms_codes`;
CREATE TABLE `sms_codes` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `phone` VARCHAR(11) NOT NULL COMMENT '手机号',
  `code` VARCHAR(6) NOT NULL COMMENT '验证码',
  `type` VARCHAR(20) NOT NULL COMMENT '类型：register,login,reset_password',
  `used` TINYINT DEFAULT 0 COMMENT '是否已使用：1-已使用，0-未使用',
  `expire_time` TIMESTAMP NOT NULL COMMENT '过期时间',
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_phone_type` (`phone`, `type`),
  KEY `idx_expire_time` (`expire_time`),
  KEY `idx_used` (`used`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='短信验证码表';
