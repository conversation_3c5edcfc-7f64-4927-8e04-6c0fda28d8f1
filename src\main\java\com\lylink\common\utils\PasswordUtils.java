package com.lylink.common.utils;

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import java.util.regex.Pattern;

/**
 * 密码工具类
 */
public class PasswordUtils {

    private static final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

    /**
     * 密码强度正则表达式
     * 至少6位，包含字母和数字
     */
    private static final String PASSWORD_REGEX = "^(?=.*[A-Za-z])(?=.*\\d)[A-Za-z\\d@$!%*?&]{6,}$";
    
    /**
     * 简单密码正则表达式
     * 至少6位字符
     */
    private static final String SIMPLE_PASSWORD_REGEX = "^.{6,}$";
    
    private static final Pattern PASSWORD_PATTERN = Pattern.compile(PASSWORD_REGEX);
    private static final Pattern SIMPLE_PASSWORD_PATTERN = Pattern.compile(SIMPLE_PASSWORD_REGEX);

    /**
     * 加密密码
     * 
     * @param rawPassword 原始密码
     * @return 加密后的密码
     */
    public static String encode(String rawPassword) {
        return passwordEncoder.encode(rawPassword);
    }

    /**
     * 验证密码
     * 
     * @param rawPassword 原始密码
     * @param encodedPassword 加密后的密码
     * @return true-密码正确，false-密码错误
     */
    public static boolean matches(String rawPassword, String encodedPassword) {
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }

    /**
     * 验证密码强度（包含字母和数字，至少6位）
     * 
     * @param password 密码
     * @return true-强度符合要求，false-强度不符合要求
     */
    public static boolean isStrongPassword(String password) {
        if (StringUtils.isEmpty(password)) {
            return false;
        }
        return PASSWORD_PATTERN.matcher(password).matches();
    }

    /**
     * 验证密码基本要求（至少6位）
     * 
     * @param password 密码
     * @return true-符合基本要求，false-不符合基本要求
     */
    public static boolean isValidPassword(String password) {
        if (StringUtils.isEmpty(password)) {
            return false;
        }
        return SIMPLE_PASSWORD_PATTERN.matcher(password).matches();
    }

    /**
     * 生成随机密码
     * 
     * @param length 密码长度
     * @return 随机密码
     */
    public static String generateRandomPassword(int length) {
        if (length < 6) {
            length = 6;
        }
        
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        StringBuilder password = new StringBuilder();
        
        for (int i = 0; i < length; i++) {
            int index = (int) (Math.random() * chars.length());
            password.append(chars.charAt(index));
        }
        
        return password.toString();
    }

    /**
     * 检查密码是否为常见弱密码
     * 
     * @param password 密码
     * @return true-是弱密码，false-不是弱密码
     */
    public static boolean isWeakPassword(String password) {
        if (StringUtils.isEmpty(password)) {
            return true;
        }
        
        String[] weakPasswords = {
            "123456", "password", "123456789", "12345678", "12345",
            "1234567", "1234567890", "qwerty", "abc123", "111111",
            "123123", "admin", "letmein", "welcome", "monkey",
            "dragon", "pass", "master", "hello", "freedom"
        };
        
        String lowerPassword = password.toLowerCase();
        for (String weak : weakPasswords) {
            if (lowerPassword.equals(weak)) {
                return true;
            }
        }
        
        // 检查是否为纯数字或纯字母
        if (password.matches("^\\d+$") || password.matches("^[a-zA-Z]+$")) {
            return true;
        }
        
        // 检查是否为重复字符
        if (password.matches("^(.)\\1+$")) {
            return true;
        }
        
        return false;
    }

    /**
     * 获取密码强度等级
     * 
     * @param password 密码
     * @return 强度等级：0-很弱，1-弱，2-中等，3-强，4-很强
     */
    public static int getPasswordStrength(String password) {
        if (StringUtils.isEmpty(password)) {
            return 0;
        }
        
        int score = 0;
        
        // 长度评分
        if (password.length() >= 8) {
            score++;
        }
        if (password.length() >= 12) {
            score++;
        }
        
        // 包含小写字母
        if (password.matches(".*[a-z].*")) {
            score++;
        }
        
        // 包含大写字母
        if (password.matches(".*[A-Z].*")) {
            score++;
        }
        
        // 包含数字
        if (password.matches(".*\\d.*")) {
            score++;
        }
        
        // 包含特殊字符
        if (password.matches(".*[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?].*")) {
            score++;
        }
        
        // 检查是否为弱密码
        if (isWeakPassword(password)) {
            score = Math.max(0, score - 2);
        }
        
        return Math.min(4, score);
    }

    /**
     * 获取密码强度描述
     * 
     * @param password 密码
     * @return 强度描述
     */
    public static String getPasswordStrengthDescription(String password) {
        int strength = getPasswordStrength(password);
        switch (strength) {
            case 0:
                return "很弱";
            case 1:
                return "弱";
            case 2:
                return "中等";
            case 3:
                return "强";
            case 4:
                return "很强";
            default:
                return "未知";
        }
    }
}
