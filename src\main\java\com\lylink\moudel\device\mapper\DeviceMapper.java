package com.lylink.moudel.device.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lylink.moudel.device.pojo.Device;
import com.lylink.moudel.device.vo.DeviceInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 设备Mapper接口
 */
@Mapper
public interface DeviceMapper extends BaseMapper<Device> {

    /**
     * 根据序列号查询设备
     * 
     * @param sn 设备序列号
     * @return 设备信息
     */
    @Select("SELECT * FROM devices WHERE sn = #{sn}")
    Device findBySn(@Param("sn") String sn);

    /**
     * 检查设备序列号是否存在
     * 
     * @param sn 设备序列号
     * @return 存在数量
     */
    @Select("SELECT COUNT(*) FROM devices WHERE sn = #{sn}")
    int countBySn(@Param("sn") String sn);

    /**
     * 获取用户绑定的设备列表
     * 
     * @param page 分页参数
     * @param userId 用户ID
     * @return 设备列表
     */
    IPage<DeviceInfoVO> getUserDevices(@Param("page") Page<DeviceInfoVO> page, @Param("userId") Long userId);

    /**
     * 获取设备详细信息
     * 
     * @param sn 设备序列号
     * @param userId 用户ID
     * @return 设备详细信息
     */
    DeviceInfoVO getDeviceDetail(@Param("sn") String sn, @Param("userId") Long userId);

    /**
     * 统计用户设备数量
     * 
     * @param userId 用户ID
     * @return 设备数量
     */
    @Select("SELECT COUNT(*) FROM devices WHERE user_id = #{userId}")
    int countUserDevices(@Param("userId") Long userId);

    /**
     * 绑定设备到用户
     * 
     * @param sn 设备序列号
     * @param userId 用户ID
     * @param alias 设备别名
     * @return 更新行数
     */
    @Update("UPDATE devices SET user_id = #{userId}, alias = #{alias}, bind_time = NOW(), updated_at = NOW() WHERE sn = #{sn}")
    int bindDevice(@Param("sn") String sn, @Param("userId") Long userId, @Param("alias") String alias);

    /**
     * 解绑设备
     * 
     * @param sn 设备序列号
     * @param userId 用户ID
     * @return 更新行数
     */
    @Update("UPDATE devices SET user_id = NULL, bind_time = NULL, updated_at = NOW() WHERE sn = #{sn} AND user_id = #{userId}")
    int unbindDevice(@Param("sn") String sn, @Param("userId") Long userId);

    /**
     * 更新设备别名
     * 
     * @param sn 设备序列号
     * @param userId 用户ID
     * @param alias 新别名
     * @return 更新行数
     */
    @Update("UPDATE devices SET alias = #{alias}, updated_at = NOW() WHERE sn = #{sn} AND user_id = #{userId}")
    int updateDeviceAlias(@Param("sn") String sn, @Param("userId") Long userId, @Param("alias") String alias);

    /**
     * 更新设备状态
     * 
     * @param sn 设备序列号
     * @param status 设备状态
     * @param lastSyncTime 最后同步时间
     * @return 更新行数
     */
    @Update("UPDATE devices SET status = #{status}, last_sync_time = #{lastSyncTime}, updated_at = NOW() WHERE sn = #{sn}")
    int updateDeviceStatus(@Param("sn") String sn, @Param("status") Integer status, @Param("lastSyncTime") LocalDateTime lastSyncTime);

    /**
     * 获取所有需要同步的设备
     * 
     * @return 设备列表
     */
    @Select("SELECT * FROM devices WHERE user_id IS NOT NULL")
    List<Device> getAllBoundDevices();

    /**
     * 获取离线设备列表
     * 
     * @param offlineThreshold 离线阈值时间
     * @return 离线设备列表
     */
    @Select("SELECT * FROM devices WHERE user_id IS NOT NULL AND (last_sync_time IS NULL OR last_sync_time < #{offlineThreshold})")
    List<Device> getOfflineDevices(@Param("offlineThreshold") LocalDateTime offlineThreshold);
}
