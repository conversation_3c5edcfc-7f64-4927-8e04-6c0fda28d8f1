package com.lylink.moudel.user.vo;

import lombok.Data;

/**
 * 登录结果VO
 */
@Data
public class LoginResultVO {

    /**
     * 访问令牌
     */
    private String accessToken;

    /**
     * 令牌类型
     */
    private String tokenType = "Bearer";

    /**
     * 过期时间（秒）
     */
    private Long expiresIn;

    /**
     * 用户信息
     */
    private UserInfoVO userInfo;

    /**
     * 是否首次登录
     */
    private Boolean isFirstLogin;
}
