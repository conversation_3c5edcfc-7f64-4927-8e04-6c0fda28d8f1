<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lylink.moudel.user.mapper.UserMapper">

    <!-- 用户详细信息结果映射 -->
    <resultMap id="UserDetailInfoMap" type="com.lylink.moudel.user.vo.UserInfoVO">
        <id column="id" property="id"/>
        <result column="phone" property="phone"/>
        <result column="nickname" property="nickname"/>
        <result column="alipay_account" property="alipayAccount"/>
        <result column="alipay_name" property="alipayName"/>
        <result column="commission_rate" property="commissionRate"/>
        <result column="settlement_cycle" property="settlementCycle"/>
        <result column="status" property="status"/>
        <result column="created_at" property="createdAt"/>
        <result column="device_count" property="deviceCount"/>
        <result column="online_device_count" property="onlineDeviceCount"/>
        <result column="today_income" property="todayIncome"/>
        <result column="total_income" property="totalIncome"/>
        <result column="available_balance" property="availableBalance"/>
    </resultMap>

    <!-- 获取用户详细信息（包含统计数据） -->
    <select id="getUserDetailInfo" resultMap="UserDetailInfoMap">
        SELECT 
            u.id,
            u.phone,
            u.nickname,
            u.alipay_account,
            u.alipay_name,
            u.commission_rate,
            u.settlement_cycle,
            u.status,
            u.created_at,
            COALESCE(device_stats.device_count, 0) as device_count,
            COALESCE(device_stats.online_device_count, 0) as online_device_count,
            COALESCE(income_stats.today_income, 0) as today_income,
            COALESCE(income_stats.total_income, 0) as total_income,
            COALESCE(income_stats.available_balance, 0) as available_balance
        FROM users u
        LEFT JOIN (
            SELECT 
                user_id,
                COUNT(*) as device_count,
                COUNT(CASE WHEN status = 1 THEN 1 END) as online_device_count
            FROM devices 
            WHERE user_id = #{userId}
            GROUP BY user_id
        ) device_stats ON u.id = device_stats.user_id
        LEFT JOIN (
            SELECT 
                user_id,
                SUM(CASE WHEN date = CURDATE() THEN actual_income ELSE 0 END) as today_income,
                SUM(actual_income) as total_income,
                SUM(actual_income) as available_balance
            FROM income_records 
            WHERE user_id = #{userId}
            GROUP BY user_id
        ) income_stats ON u.id = income_stats.user_id
        WHERE u.id = #{userId}
    </select>

    <!-- 更新用户最后登录时间 -->
    <update id="updateLastLoginTime">
        UPDATE users 
        SET updated_at = NOW() 
        WHERE id = #{userId}
    </update>

</mapper>
