-- 灵源Link平台初始化数据脚本

USE `lingyuanlink`;

-- 插入默认管理员账号
INSERT INTO `admins` (`username`, `password_hash`, `real_name`, `role`, `status`) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTrEPokxbmf9GFpTdu9.x3KLDxTBcGSS', '系统管理员', 'super', 1),
('operator', '$2a$10$N.zmdr9k7uOCQb376NoUnuTrEPokxbmf9GFpTdu9.x3KLDxTBcGSS', '运营管理员', 'admin', 1);
-- 默认密码都是: admin123

-- 插入系统配置
INSERT INTO `system_config` (`config_key`, `config_value`, `description`, `config_type`, `is_public`) VALUES
('platform.name', '灵源Link', '平台名称', 'string', 1),
('platform.version', '1.0.0', '平台版本', 'string', 1),
('platform.logo', '', '平台Logo URL', 'string', 1),

-- 收益相关配置
('income.default_commission_rate', '0.1000', '默认抽成比例', 'number', 0),
('income.min_commission_rate', '0.0500', '最小抽成比例', 'number', 0),
('income.max_commission_rate', '0.3000', '最大抽成比例', 'number', 0),
('income.settlement_cycle', 'monthly', '默认结算周期', 'string', 0),

-- 提现相关配置
('withdraw.min_amount', '10.00', '最小提现金额', 'number', 1),
('withdraw.max_amount', '10000.00', '最大提现金额', 'number', 1),
('withdraw.daily_limit', '50000.00', '每日提现限额', 'number', 0),
('withdraw.fee_rate', '0.0000', '提现手续费率', 'number', 1),
('withdraw.auto_audit_amount', '100.00', '自动审核金额阈值', 'number', 0),

-- 设备相关配置
('device.max_bind_count', '10', '用户最大绑定设备数', 'number', 1),
('device.sync_interval', '30', '设备状态同步间隔(分钟)', 'number', 0),
('device.offline_threshold', '60', '设备离线判定阈值(分钟)', 'number', 0),

-- 短信相关配置
('sms.code_length', '6', '验证码长度', 'number', 0),
('sms.expire_minutes', '5', '验证码有效期(分钟)', 'number', 0),
('sms.daily_limit', '10', '每日发送限制', 'number', 0),
('sms.provider', 'aliyun', '短信服务商', 'string', 0),

-- 上游API配置
('upstream.api_base_url', 'https://gw.fudiancloud.com', '上游API基础URL', 'string', 0),
('upstream.api_timeout', '30', 'API超时时间(秒)', 'number', 0),
('upstream.sync_batch_size', '100', '批量同步数量', 'number', 0),

-- 安全相关配置
('security.jwt_secret', 'lingyuanlink_jwt_secret_key_2024', 'JWT密钥', 'string', 0),
('security.jwt_expire_hours', '168', 'JWT过期时间(小时)', 'number', 0),
('security.password_min_length', '6', '密码最小长度', 'number', 1),
('security.login_max_attempts', '5', '登录最大尝试次数', 'number', 0),
('security.lock_duration_minutes', '30', '账号锁定时长(分钟)', 'number', 0),

-- 系统运行配置
('system.maintenance_mode', 'false', '维护模式', 'boolean', 1),
('system.maintenance_message', '系统维护中，请稍后再试', '维护提示信息', 'string', 1),
('system.register_enabled', 'true', '是否允许注册', 'boolean', 1),
('system.auto_bind_enabled', 'false', '是否允许自动绑定设备', 'boolean', 0),

-- 通知相关配置
('notification.email_enabled', 'false', '邮件通知开关', 'boolean', 0),
('notification.sms_enabled', 'true', '短信通知开关', 'boolean', 0),
('notification.withdraw_notify', 'true', '提现通知开关', 'boolean', 0),

-- 数据统计配置
('stats.retention_days', '365', '数据保留天数', 'number', 0),
('stats.report_enabled', 'true', '统计报表开关', 'boolean', 0),
('stats.export_enabled', 'true', '数据导出开关', 'boolean', 0);

-- 插入测试用户数据 (可选，生产环境可删除)
INSERT INTO `users` (`phone`, `password_hash`, `nickname`, `commission_rate`, `status`) VALUES
('13800138000', '$2a$10$N.zmdr9k7uOCQb376NoUnuTrEPokxbmf9GFpTdu9.x3KLDxTBcGSS', '测试用户1', 0.1000, 1),
('13800138001', '$2a$10$N.zmdr9k7uOCQb376NoUnuTrEPokxbmf9GFpTdu9.x3KLDxTBcGSS', '测试用户2', 0.1200, 1);
-- 默认密码都是: 123456

-- 插入测试设备数据 (可选，生产环境可删除)
INSERT INTO `devices` (`sn`, `alias`, `device_type`, `status`) VALUES
('LY001001', '测试设备1', 'router', 1),
('LY001002', '测试设备2', 'router', 1),
('LY001003', '测试设备3', 'router', 0),
('LY001004', '测试设备4', 'router', 1),
('LY001005', '测试设备5', 'router', 1);

-- 创建索引优化查询性能
CREATE INDEX idx_income_records_user_date ON income_records(user_id, date DESC);
CREATE INDEX idx_withdraw_applications_status_apply_time ON withdraw_applications(status, apply_time DESC);
CREATE INDEX idx_operation_logs_user_created ON operation_logs(user_type, user_id, created_at DESC);
CREATE INDEX idx_devices_user_status ON devices(user_id, status);

-- 创建视图简化查询
CREATE VIEW v_user_device_summary AS
SELECT 
    u.id as user_id,
    u.phone,
    u.nickname,
    u.commission_rate,
    COUNT(d.id) as device_count,
    COUNT(CASE WHEN d.status = 1 THEN 1 END) as online_device_count,
    u.status as user_status,
    u.created_at as register_time
FROM users u
LEFT JOIN devices d ON u.id = d.user_id
GROUP BY u.id;

CREATE VIEW v_user_income_summary AS
SELECT 
    user_id,
    DATE_FORMAT(date, '%Y-%m') as month,
    SUM(upstream_income) as total_upstream_income,
    SUM(commission) as total_commission,
    SUM(actual_income) as total_actual_income,
    COUNT(*) as record_count
FROM income_records
GROUP BY user_id, DATE_FORMAT(date, '%Y-%m');

-- 创建存储过程用于数据清理
DELIMITER //
CREATE PROCEDURE CleanExpiredData()
BEGIN
    -- 清理过期的短信验证码 (保留7天)
    DELETE FROM sms_codes WHERE created_at < DATE_SUB(NOW(), INTERVAL 7 DAY);
    
    -- 清理过期的操作日志 (保留90天)
    DELETE FROM operation_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY);
    
    -- 清理过期的收益记录 (根据配置保留)
    SET @retention_days = (SELECT config_value FROM system_config WHERE config_key = 'stats.retention_days');
    SET @sql = CONCAT('DELETE FROM income_records WHERE created_at < DATE_SUB(NOW(), INTERVAL ', @retention_days, ' DAY)');
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
END //
DELIMITER ;

-- 创建触发器维护数据一致性
DELIMITER //
CREATE TRIGGER tr_device_bind_log AFTER UPDATE ON devices
FOR EACH ROW
BEGIN
    IF OLD.user_id != NEW.user_id OR (OLD.user_id IS NULL AND NEW.user_id IS NOT NULL) THEN
        INSERT INTO operation_logs (user_type, user_id, operation, description, status)
        VALUES ('admin', 0, 'device_bind', 
                CONCAT('设备 ', NEW.sn, ' 绑定到用户 ', IFNULL(NEW.user_id, '无')), 1);
    END IF;
END //
DELIMITER ;

DELIMITER //
CREATE TRIGGER tr_withdraw_status_log AFTER UPDATE ON withdraw_applications
FOR EACH ROW
BEGIN
    IF OLD.status != NEW.status THEN
        INSERT INTO operation_logs (user_type, user_id, operation, description, status)
        VALUES ('admin', IFNULL(NEW.audit_admin_id, 0), 'withdraw_audit', 
                CONCAT('提现申请 ', NEW.id, ' 状态变更: ', OLD.status, ' -> ', NEW.status), 1);
    END IF;
END //
DELIMITER ;

-- 提交事务
COMMIT;
