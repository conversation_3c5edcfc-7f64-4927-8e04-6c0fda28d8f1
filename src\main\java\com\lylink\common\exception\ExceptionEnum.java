package com.lylink.common.exception;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
public enum ExceptionEnum {

    BASE_FAIL(500,"请联系网站管理员解决问题"),
    NOT_FOUND(404,"资源未找到"),
    FAIL_RME(403,"请求方法错误"),
    FAIL_RME_PARAM(406,"请求参数错误"),
    FAIL_TOKEN(401,"token失效"),
    FAIL_DATADUPLICATION(405,"你输入的数据重复，请更换之后在重试"),
    FAIL_CONSTRAINTERROR(405,"数据约束错误,请联系网站负责人处理"),
    VERIFICATION_FAILED(401,"验证失败"),
    OPERATION_FAILED(400,"操作失败"),
    LOGIN_FAIL(400,"登录失败"),
    TYPE_ERROR(404,"没有这个板块"),
    ACCESS_DENIEDEXCEPTION(403, "权限不足，不允许访问"),

    // 用户相关异常
    USER_NOT_FOUND(404, "用户不存在"),
    USER_ALREADY_EXISTS(409, "用户已存在"),
    USER_DISABLED(403, "用户已被禁用"),
    PHONE_ALREADY_EXISTS(409, "手机号已被注册"),
    INVALID_PHONE(400, "手机号格式不正确"),
    INVALID_PASSWORD(400, "密码格式不正确"),
    PASSWORD_NOT_MATCH(400, "密码不匹配"),

    // 验证码相关异常
    SMS_CODE_EXPIRED(400, "验证码已过期"),
    SMS_CODE_INVALID(400, "验证码不正确"),
    SMS_CODE_USED(400, "验证码已使用"),
    SMS_SEND_FAILED(500, "短信发送失败"),
    SMS_SEND_TOO_FREQUENT(429, "短信发送过于频繁"),
    SMS_DAILY_LIMIT_EXCEEDED(429, "今日短信发送次数已达上限"),

    // 设备相关异常
    DEVICE_NOT_FOUND(404, "设备不存在"),
    DEVICE_ALREADY_BOUND(409, "设备已被绑定"),
    DEVICE_NOT_BOUND(400, "设备未绑定"),
    DEVICE_BIND_LIMIT_EXCEEDED(400, "设备绑定数量已达上限"),
    DEVICE_OFFLINE(400, "设备离线"),
    DEVICE_SYNC_FAILED(500, "设备同步失败"),

    // 收益相关异常
    INCOME_NOT_FOUND(404, "收益记录不存在"),
    INCOME_CALCULATION_FAILED(500, "收益计算失败"),
    INSUFFICIENT_BALANCE(400, "余额不足"),

    // 提现相关异常
    WITHDRAW_NOT_FOUND(404, "提现申请不存在"),
    WITHDRAW_AMOUNT_INVALID(400, "提现金额不合法"),
    WITHDRAW_AMOUNT_TOO_SMALL(400, "提现金额过小"),
    WITHDRAW_AMOUNT_TOO_LARGE(400, "提现金额过大"),
    WITHDRAW_DAILY_LIMIT_EXCEEDED(400, "今日提现金额已达上限"),
    WITHDRAW_ALREADY_PROCESSED(400, "提现申请已处理"),

    // 系统配置相关异常
    CONFIG_NOT_FOUND(404, "配置项不存在"),
    CONFIG_UPDATE_FAILED(500, "配置更新失败"),

    // 上游API相关异常
    UPSTREAM_API_ERROR(500, "上游API调用失败"),
    UPSTREAM_API_TIMEOUT(504, "上游API调用超时"),
    UPSTREAM_API_RATE_LIMIT(429, "上游API调用频率限制"),

    // 业务逻辑异常
    BUSINESS_LOGIC_ERROR(400, "业务逻辑错误"),
    DATA_SYNC_FAILED(500, "数据同步失败"),
    MAINTENANCE_MODE(503, "系统维护中");


    public Integer code;

    public String message;


}
