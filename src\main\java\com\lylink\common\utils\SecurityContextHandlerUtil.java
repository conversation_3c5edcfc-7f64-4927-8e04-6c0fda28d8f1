package com.lylink.common.utils;

import com.lylink.common.commonVo.UserDetailsVo;
import com.lylink.moudel.user.pojo.User;
import org.springframework.security.core.context.SecurityContextHolder;

public class SecurityContextHandlerUtil {

    public static User getUser(){
        Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (principal instanceof UserDetailsVo) {
            return  ((UserDetailsVo) principal).getUser() ;
        }
        return null;
    }
}
