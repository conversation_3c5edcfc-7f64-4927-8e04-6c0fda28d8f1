package com.lylink.moudel.user.pojo;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 短信验证码实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("sms_codes")
public class SmsCode implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 手机号
     */
    @TableField("phone")
    private String phone;

    /**
     * 验证码
     */
    @TableField("code")
    private String code;

    /**
     * 类型：register,login,reset_password
     */
    @TableField("type")
    private String type;

    /**
     * 是否已使用：1-已使用，0-未使用
     */
    @TableField("used")
    private Integer used;

    /**
     * 过期时间
     */
    @TableField("expire_time")
    private LocalDateTime expireTime;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
}
