package com.lylink.moudel.device.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 设备绑定DTO
 */
@Data
public class DeviceBindDTO {

    /**
     * 设备序列号
     */
    @NotBlank(message = "设备序列号不能为空")
    @Pattern(regexp = "^[A-Za-z0-9]{6,20}$", message = "设备序列号格式不正确")
    private String sn;

    /**
     * 设备别名（可选）
     */
    @Size(max = 50, message = "设备别名长度不能超过50个字符")
    private String alias;
}
