package com.lylink.moudel.income.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 收益概览VO
 */
@Data
public class IncomeSummaryVO {

    /**
     * 今日收益
     */
    private BigDecimal todayIncome;

    /**
     * 昨日收益
     */
    private BigDecimal yesterdayIncome;

    /**
     * 本月收益
     */
    private BigDecimal monthIncome;

    /**
     * 上月收益
     */
    private BigDecimal lastMonthIncome;

    /**
     * 总收益
     */
    private BigDecimal totalIncome;

    /**
     * 可提现余额
     */
    private BigDecimal availableBalance;

    /**
     * 今日流量(MB)
     */
    private Long todayTraffic;

    /**
     * 本月流量(MB)
     */
    private Long monthTraffic;

    /**
     * 总流量(MB)
     */
    private Long totalTraffic;

    /**
     * 在线设备数
     */
    private Integer onlineDeviceCount;

    /**
     * 总设备数
     */
    private Integer totalDeviceCount;

    /**
     * 今日在线时长(分钟)
     */
    private Integer todayOnlineDuration;

    /**
     * 收益增长率(%)
     */
    private BigDecimal incomeGrowthRate;

    /**
     * 流量增长率(%)
     */
    private BigDecimal trafficGrowthRate;
}
