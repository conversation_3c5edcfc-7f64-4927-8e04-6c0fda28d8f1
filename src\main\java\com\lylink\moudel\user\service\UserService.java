package com.lylink.moudel.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lylink.moudel.user.dto.*;
import com.lylink.moudel.user.pojo.User;
import com.lylink.moudel.user.vo.LoginResultVO;
import com.lylink.moudel.user.vo.UserInfoVO;

/**
 * 用户服务接口
 */
public interface UserService extends IService<User> {

    /**
     * 发送短信验证码
     * 
     * @param smsCodeDTO 短信验证码DTO
     * @return 发送结果
     */
    boolean sendSmsCode(SmsCodeDTO smsCodeDTO);

    /**
     * 用户注册
     * 
     * @param registerDTO 注册DTO
     * @return 注册结果
     */
    boolean register(UserRegisterDTO registerDTO);

    /**
     * 用户登录
     * 
     * @param loginDTO 登录DTO
     * @return 登录结果
     */
    LoginResultVO login(UserLoginDTO loginDTO);

    /**
     * 获取用户信息
     * 
     * @param userId 用户ID
     * @return 用户信息
     */
    UserInfoVO getUserInfo(Long userId);

    /**
     * 更新用户信息
     * 
     * @param userId 用户ID
     * @param updateDTO 更新DTO
     * @return 更新结果
     */
    boolean updateUserInfo(Long userId, UserUpdateDTO updateDTO);

    /**
     * 重置密码
     * 
     * @param resetDTO 重置密码DTO
     * @return 重置结果
     */
    boolean resetPassword(PasswordResetDTO resetDTO);

    /**
     * 根据手机号查询用户
     * 
     * @param phone 手机号
     * @return 用户信息
     */
    User findByPhone(String phone);

    /**
     * 检查手机号是否已存在
     * 
     * @param phone 手机号
     * @return 是否存在
     */
    boolean isPhoneExists(String phone);

    /**
     * 验证短信验证码
     * 
     * @param phone 手机号
     * @param code 验证码
     * @param type 验证码类型
     * @return 验证结果
     */
    boolean verifySmsCode(String phone, String code, String type);

    /**
     * 刷新用户token
     * 
     * @param userId 用户ID
     * @return 新的token
     */
    String refreshToken(Long userId);
}
