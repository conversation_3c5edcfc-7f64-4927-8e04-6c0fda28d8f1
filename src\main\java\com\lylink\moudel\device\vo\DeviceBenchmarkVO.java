package com.lylink.moudel.device.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 设备性能测试VO
 */
@Data
public class DeviceBenchmarkVO {

    /**
     * 设备序列号
     */
    private String sn;

    /**
     * 测试状态：running-运行中，completed-已完成，failed-失败
     */
    private String status;

    /**
     * 测试进度(0-100)
     */
    private Integer progress;

    /**
     * 网络延迟(ms)
     */
    private Integer latency;

    /**
     * 下载速度(Mbps)
     */
    private Double downloadSpeed;

    /**
     * 上传速度(Mbps)
     */
    private Double uploadSpeed;

    /**
     * 丢包率(%)
     */
    private Double packetLoss;

    /**
     * CPU使用率(%)
     */
    private Double cpuUsage;

    /**
     * 内存使用率(%)
     */
    private Double memoryUsage;

    /**
     * 磁盘使用率(%)
     */
    private Double diskUsage;

    /**
     * 温度(℃)
     */
    private Double temperature;

    /**
     * 测试开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 测试完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 测试结果详情
     */
    private Object details;
}
