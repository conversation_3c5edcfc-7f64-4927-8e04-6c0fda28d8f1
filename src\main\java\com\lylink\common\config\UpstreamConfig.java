package com.lylink.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 上游API配置类
 */
@Data
@Component
@ConfigurationProperties(prefix = "upstream.api")
public class UpstreamConfig {

    /**
     * 上游API基础URL
     */
    private String baseUrl = "https://gw.fudiancloud.com";

    /**
     * 请求超时时间(毫秒)
     */
    private Integer timeout = 30000;

    /**
     * 重试次数
     */
    private Integer retryCount = 3;

    /**
     * API密钥
     */
    private String apiKey;

    /**
     * API密钥
     */
    private String apiSecret;

    /**
     * 获取完整的API URL
     * 
     * @param path API路径
     * @return 完整URL
     */
    public String getFullUrl(String path) {
        if (path.startsWith("/")) {
            return baseUrl + path;
        } else {
            return baseUrl + "/" + path;
        }
    }

    /**
     * 常用API路径常量
     */
    public static class ApiPaths {
        public static final String LOGIN = "/api/auth/login";
        public static final String DEVICES = "/api/devices";
        public static final String DEVICE_DETAIL = "/api/devices/{sn}";
        public static final String DEVICE_TRAFFIC = "/api/devices/{sn}/traffic";
        public static final String DEVICE_BENCHMARK = "/api/devices/{sn}/benchmark";
        public static final String WALLET_SUMMARY = "/api/wallet/summary";
        public static final String INCOME_DAILY = "/api/income/daily";
        public static final String INCOME_MONTHLY = "/api/income/monthly";
    }
}
