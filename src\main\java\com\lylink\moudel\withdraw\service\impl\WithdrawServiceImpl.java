package com.lylink.moudel.withdraw.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lylink.common.config.BusinessConfig;
import com.lylink.common.constant.BusinessConstants;
import com.lylink.common.exception.CustomaizeExpetion;
import com.lylink.common.exception.ExceptionEnum;
import com.lylink.common.utils.StringUtils;
import com.lylink.moudel.income.service.IncomeService;
import com.lylink.moudel.user.mapper.UserMapper;
import com.lylink.moudel.user.pojo.User;
import com.lylink.moudel.withdraw.dto.WithdrawApplyDTO;
import com.lylink.moudel.withdraw.mapper.WithdrawApplicationMapper;
import com.lylink.moudel.withdraw.pojo.WithdrawApplication;
import com.lylink.moudel.withdraw.service.WithdrawService;
import com.lylink.moudel.withdraw.vo.WithdrawRecordVO;
import com.lylink.moudel.withdraw.vo.WithdrawSummaryVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

/**
 * 提现服务实现类
 */
@Slf4j
@Service
public class WithdrawServiceImpl extends ServiceImpl<WithdrawApplicationMapper, WithdrawApplication> implements WithdrawService {

    @Autowired
    private WithdrawApplicationMapper withdrawApplicationMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private IncomeService incomeService;

    @Autowired
    private BusinessConfig businessConfig;

    @Override
    public WithdrawSummaryVO getUserWithdrawSummary(Long userId) {
        WithdrawSummaryVO summary = withdrawApplicationMapper.getUserWithdrawSummary(userId);
        if (summary == null) {
            summary = new WithdrawSummaryVO();
            summary.setAvailableBalance(BigDecimal.ZERO);
            summary.setTodayWithdrawn(BigDecimal.ZERO);
            summary.setTotalWithdrawn(BigDecimal.ZERO);
            summary.setPendingCount(0);
            summary.setProcessingCount(0);
        }

        // 设置配置信息
        summary.setMinAmount(businessConfig.getWithdraw().getMinAmount());
        summary.setMaxAmount(businessConfig.getWithdraw().getMaxAmount());
        summary.setFeeRate(businessConfig.getWithdraw().getFeeRate());

        // 计算今日剩余提现额度
        BigDecimal dailyLimit = businessConfig.getWithdraw().getDailyLimit();
        BigDecimal todayRemaining = dailyLimit.subtract(summary.getTodayWithdrawn());
        if (todayRemaining.compareTo(BigDecimal.ZERO) < 0) {
            todayRemaining = BigDecimal.ZERO;
        }
        summary.setTodayRemaining(todayRemaining);

        // 检查是否可以提现
        WithdrawCheckResult checkResult = checkWithdrawEligibility(userId);
        summary.setCanWithdraw(checkResult.isCanWithdraw());
        summary.setCannotWithdrawReason(checkResult.getReason());

        return summary;
    }

    @Override
    public IPage<WithdrawRecordVO> getUserWithdrawRecords(Long userId, int page, int size, 
                                                          String status, LocalDate startDate, LocalDate endDate) {
        Page<WithdrawRecordVO> pageParam = new Page<>(page, size);
        return withdrawApplicationMapper.getUserWithdrawRecords(pageParam, userId, status, startDate, endDate);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String applyWithdraw(Long userId, WithdrawApplyDTO applyDTO) {
        BigDecimal amount = applyDTO.getAmount();

        // 检查用户是否存在
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new CustomaizeExpetion(ExceptionEnum.USER_NOT_FOUND);
        }

        // 检查用户状态
        if (!BusinessConstants.UserStatus.NORMAL.equals(user.getStatus())) {
            throw new CustomaizeExpetion(ExceptionEnum.USER_DISABLED);
        }

        // 检查是否可以提现
        WithdrawCheckResult checkResult = checkWithdrawEligibility(userId);
        if (!checkResult.isCanWithdraw()) {
            throw new CustomaizeExpetion(ExceptionEnum.BUSINESS_LOGIC_ERROR.code, checkResult.getReason());
        }

        // 检查提现金额
        if (!isValidWithdrawAmount(userId, amount)) {
            throw new CustomaizeExpetion(ExceptionEnum.WITHDRAW_AMOUNT_INVALID);
        }

        // 检查余额是否足够
        BigDecimal availableBalance = incomeService.getUserAvailableBalance(userId);
        if (availableBalance.compareTo(amount) < 0) {
            throw new CustomaizeExpetion(ExceptionEnum.INSUFFICIENT_BALANCE);
        }

        // 检查今日提现限额
        BigDecimal todayWithdrawn = withdrawApplicationMapper.getUserTodayWithdrawAmount(userId, LocalDate.now());
        BigDecimal dailyLimit = businessConfig.getWithdraw().getDailyLimit();
        if (todayWithdrawn.add(amount).compareTo(dailyLimit) > 0) {
            throw new CustomaizeExpetion(ExceptionEnum.WITHDRAW_DAILY_LIMIT_EXCEEDED);
        }

        // 创建提现申请
        WithdrawApplication application = new WithdrawApplication();
        application.setId(UUID.randomUUID().toString());
        application.setUserId(userId);
        application.setAmount(amount);
        application.setAlipayAccount(applyDTO.getAlipayAccount());
        application.setAlipayName(applyDTO.getAlipayName());
        application.setStatus(BusinessConstants.WithdrawStatus.PENDING);
        application.setRemark(applyDTO.getRemark());

        boolean result = save(application);
        if (result) {
            log.info("提现申请提交成功: userId={}, applicationId={}, amount={}", 
                    userId, application.getId(), amount);
            return application.getId();
        } else {
            throw new CustomaizeExpetion(ExceptionEnum.OPERATION_FAILED);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelWithdraw(Long userId, String applicationId) {
        // 查询申请
        WithdrawApplication application = getById(applicationId);
        if (application == null) {
            throw new CustomaizeExpetion(ExceptionEnum.WITHDRAW_NOT_FOUND);
        }

        // 检查申请是否属于该用户
        if (!userId.equals(application.getUserId())) {
            throw new CustomaizeExpetion(ExceptionEnum.ACCESS_DENIEDEXCEPTION);
        }

        // 检查申请状态是否可以取消
        if (!BusinessConstants.WithdrawStatus.PENDING.equals(application.getStatus())) {
            throw new CustomaizeExpetion(ExceptionEnum.WITHDRAW_ALREADY_PROCESSED);
        }

        // 更新状态为已拒绝
        application.setStatus(BusinessConstants.WithdrawStatus.REJECTED);
        application.setAuditReason("用户主动取消");
        application.setAuditTime(java.time.LocalDateTime.now());

        boolean result = updateById(application);
        if (result) {
            log.info("提现申请取消成功: userId={}, applicationId={}", userId, applicationId);
        }

        return result;
    }

    @Override
    public WithdrawCheckResult checkWithdrawEligibility(Long userId) {
        // 检查是否有进行中的申请
        int activeCount = withdrawApplicationMapper.getUserActiveWithdrawCount(userId);
        if (activeCount > 0) {
            return new WithdrawCheckResult(false, "您有正在处理的提现申请，请等待处理完成");
        }

        // 检查可提现余额
        BigDecimal availableBalance = incomeService.getUserAvailableBalance(userId);
        BigDecimal minAmount = businessConfig.getWithdraw().getMinAmount();
        if (availableBalance.compareTo(minAmount) < 0) {
            return new WithdrawCheckResult(false, "可提现余额不足，最小提现金额为" + minAmount + "元");
        }

        // 检查今日提现限额
        BigDecimal todayWithdrawn = withdrawApplicationMapper.getUserTodayWithdrawAmount(userId, LocalDate.now());
        BigDecimal dailyLimit = businessConfig.getWithdraw().getDailyLimit();
        if (todayWithdrawn.compareTo(dailyLimit) >= 0) {
            return new WithdrawCheckResult(false, "今日提现额度已用完，每日限额为" + dailyLimit + "元");
        }

        return new WithdrawCheckResult(true, null);
    }

    @Override
    public boolean isValidWithdrawAmount(Long userId, BigDecimal amount) {
        if (amount == null) {
            return false;
        }

        BigDecimal minAmount = businessConfig.getWithdraw().getMinAmount();
        BigDecimal maxAmount = businessConfig.getWithdraw().getMaxAmount();

        // 检查金额范围
        if (amount.compareTo(minAmount) < 0 || amount.compareTo(maxAmount) > 0) {
            return false;
        }

        // 检查可提现余额
        BigDecimal availableBalance = incomeService.getUserAvailableBalance(userId);
        if (amount.compareTo(availableBalance) > 0) {
            return false;
        }

        // 检查今日剩余额度
        BigDecimal todayWithdrawn = withdrawApplicationMapper.getUserTodayWithdrawAmount(userId, LocalDate.now());
        BigDecimal dailyLimit = businessConfig.getWithdraw().getDailyLimit();
        BigDecimal todayRemaining = dailyLimit.subtract(todayWithdrawn);
        if (amount.compareTo(todayRemaining) > 0) {
            return false;
        }

        return true;
    }
}
