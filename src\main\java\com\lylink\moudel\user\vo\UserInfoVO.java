package com.lylink.moudel.user.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户信息VO
 */
@Data
public class UserInfoVO {

    /**
     * 用户ID
     */
    private Long id;

    /**
     * 手机号（脱敏）
     */
    private String phone;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 支付宝账号（脱敏）
     */
    private String alipayAccount;

    /**
     * 支付宝姓名
     */
    private String alipayName;

    /**
     * 抽成比例
     */
    private BigDecimal commissionRate;

    /**
     * 结算周期
     */
    private String settlementCycle;

    /**
     * 状态：1-正常，0-禁用
     */
    private Integer status;

    /**
     * 注册时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 设备绑定数量
     */
    private Integer deviceCount;

    /**
     * 在线设备数量
     */
    private Integer onlineDeviceCount;

    /**
     * 今日收益
     */
    private BigDecimal todayIncome;

    /**
     * 总收益
     */
    private BigDecimal totalIncome;

    /**
     * 可提现余额
     */
    private BigDecimal availableBalance;
}
