package com.lylink.common.utils;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;

import java.util.Map;

/**
 * HTTP客户端工具类
 * 用于调用上游API
 */
@Slf4j
public class HttpClientUtils {

    private static final RestTemplate restTemplate = new RestTemplate();

    /**
     * GET请求
     * 
     * @param url 请求URL
     * @param headers 请求头
     * @param responseType 响应类型
     * @return 响应结果
     */
    public static <T> T get(String url, HttpHeaders headers, Class<T> responseType) {
        try {
            HttpEntity<String> entity = new HttpEntity<>(headers);
            ResponseEntity<T> response = restTemplate.exchange(url, HttpMethod.GET, entity, responseType);
            
            log.info("GET请求成功: {} -> {}", url, response.getStatusCode());
            return response.getBody();
        } catch (ResourceAccessException e) {
            log.error("GET请求超时: {}", url, e);
            throw new RuntimeException("请求超时", e);
        } catch (HttpClientErrorException | HttpServerErrorException e) {
            log.error("GET请求失败: {} -> {}", url, e.getStatusCode(), e);
            throw new RuntimeException("请求失败: " + e.getStatusCode(), e);
        } catch (Exception e) {
            log.error("GET请求异常: {}", url, e);
            throw new RuntimeException("请求异常", e);
        }
    }

    /**
     * GET请求（无请求头）
     * 
     * @param url 请求URL
     * @param responseType 响应类型
     * @return 响应结果
     */
    public static <T> T get(String url, Class<T> responseType) {
        return get(url, new HttpHeaders(), responseType);
    }

    /**
     * POST请求
     * 
     * @param url 请求URL
     * @param requestBody 请求体
     * @param headers 请求头
     * @param responseType 响应类型
     * @return 响应结果
     */
    public static <T> T post(String url, Object requestBody, HttpHeaders headers, Class<T> responseType) {
        try {
            if (headers.getContentType() == null) {
                headers.setContentType(MediaType.APPLICATION_JSON);
            }
            
            String jsonBody = requestBody instanceof String ? 
                (String) requestBody : JSON.toJSONString(requestBody);
            
            HttpEntity<String> entity = new HttpEntity<>(jsonBody, headers);
            ResponseEntity<T> response = restTemplate.exchange(url, HttpMethod.POST, entity, responseType);
            
            log.info("POST请求成功: {} -> {}", url, response.getStatusCode());
            return response.getBody();
        } catch (ResourceAccessException e) {
            log.error("POST请求超时: {}", url, e);
            throw new RuntimeException("请求超时", e);
        } catch (HttpClientErrorException | HttpServerErrorException e) {
            log.error("POST请求失败: {} -> {}", url, e.getStatusCode(), e);
            throw new RuntimeException("请求失败: " + e.getStatusCode(), e);
        } catch (Exception e) {
            log.error("POST请求异常: {}", url, e);
            throw new RuntimeException("请求异常", e);
        }
    }

    /**
     * POST请求（无请求头）
     * 
     * @param url 请求URL
     * @param requestBody 请求体
     * @param responseType 响应类型
     * @return 响应结果
     */
    public static <T> T post(String url, Object requestBody, Class<T> responseType) {
        return post(url, requestBody, new HttpHeaders(), responseType);
    }

    /**
     * PUT请求
     * 
     * @param url 请求URL
     * @param requestBody 请求体
     * @param headers 请求头
     * @param responseType 响应类型
     * @return 响应结果
     */
    public static <T> T put(String url, Object requestBody, HttpHeaders headers, Class<T> responseType) {
        try {
            if (headers.getContentType() == null) {
                headers.setContentType(MediaType.APPLICATION_JSON);
            }
            
            String jsonBody = requestBody instanceof String ? 
                (String) requestBody : JSON.toJSONString(requestBody);
            
            HttpEntity<String> entity = new HttpEntity<>(jsonBody, headers);
            ResponseEntity<T> response = restTemplate.exchange(url, HttpMethod.PUT, entity, responseType);
            
            log.info("PUT请求成功: {} -> {}", url, response.getStatusCode());
            return response.getBody();
        } catch (ResourceAccessException e) {
            log.error("PUT请求超时: {}", url, e);
            throw new RuntimeException("请求超时", e);
        } catch (HttpClientErrorException | HttpServerErrorException e) {
            log.error("PUT请求失败: {} -> {}", url, e.getStatusCode(), e);
            throw new RuntimeException("请求失败: " + e.getStatusCode(), e);
        } catch (Exception e) {
            log.error("PUT请求异常: {}", url, e);
            throw new RuntimeException("请求异常", e);
        }
    }

    /**
     * DELETE请求
     * 
     * @param url 请求URL
     * @param headers 请求头
     * @param responseType 响应类型
     * @return 响应结果
     */
    public static <T> T delete(String url, HttpHeaders headers, Class<T> responseType) {
        try {
            HttpEntity<String> entity = new HttpEntity<>(headers);
            ResponseEntity<T> response = restTemplate.exchange(url, HttpMethod.DELETE, entity, responseType);
            
            log.info("DELETE请求成功: {} -> {}", url, response.getStatusCode());
            return response.getBody();
        } catch (ResourceAccessException e) {
            log.error("DELETE请求超时: {}", url, e);
            throw new RuntimeException("请求超时", e);
        } catch (HttpClientErrorException | HttpServerErrorException e) {
            log.error("DELETE请求失败: {} -> {}", url, e.getStatusCode(), e);
            throw new RuntimeException("请求失败: " + e.getStatusCode(), e);
        } catch (Exception e) {
            log.error("DELETE请求异常: {}", url, e);
            throw new RuntimeException("请求异常", e);
        }
    }

    /**
     * 创建带认证的请求头
     * 
     * @param token 认证token
     * @return 请求头
     */
    public static HttpHeaders createAuthHeaders(String token) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(token);
        return headers;
    }

    /**
     * 创建基本请求头
     * 
     * @return 请求头
     */
    public static HttpHeaders createBasicHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        return headers;
    }

    /**
     * 创建带自定义头的请求头
     * 
     * @param customHeaders 自定义头
     * @return 请求头
     */
    public static HttpHeaders createCustomHeaders(Map<String, String> customHeaders) {
        HttpHeaders headers = createBasicHeaders();
        if (customHeaders != null) {
            customHeaders.forEach(headers::set);
        }
        return headers;
    }

    /**
     * 构建URL参数
     * 
     * @param baseUrl 基础URL
     * @param params 参数
     * @return 完整URL
     */
    public static String buildUrl(String baseUrl, Map<String, Object> params) {
        if (params == null || params.isEmpty()) {
            return baseUrl;
        }
        
        StringBuilder url = new StringBuilder(baseUrl);
        url.append("?");
        
        params.forEach((key, value) -> {
            if (value != null) {
                url.append(key).append("=").append(value).append("&");
            }
        });
        
        // 移除最后一个&
        if (url.charAt(url.length() - 1) == '&') {
            url.deleteCharAt(url.length() - 1);
        }
        
        return url.toString();
    }
}
