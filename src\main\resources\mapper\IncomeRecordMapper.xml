<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lylink.moudel.income.mapper.IncomeRecordMapper">

    <!-- 收益概览结果映射 -->
    <resultMap id="IncomeSummaryMap" type="com.lylink.moudel.income.vo.IncomeSummaryVO">
        <result column="today_income" property="todayIncome"/>
        <result column="yesterday_income" property="yesterdayIncome"/>
        <result column="month_income" property="monthIncome"/>
        <result column="last_month_income" property="lastMonthIncome"/>
        <result column="total_income" property="totalIncome"/>
        <result column="available_balance" property="availableBalance"/>
        <result column="today_traffic" property="todayTraffic"/>
        <result column="month_traffic" property="monthTraffic"/>
        <result column="total_traffic" property="totalTraffic"/>
        <result column="online_device_count" property="onlineDeviceCount"/>
        <result column="total_device_count" property="totalDeviceCount"/>
        <result column="today_online_duration" property="todayOnlineDuration"/>
        <result column="income_growth_rate" property="incomeGrowthRate"/>
        <result column="traffic_growth_rate" property="trafficGrowthRate"/>
    </resultMap>

    <!-- 收益明细结果映射 -->
    <resultMap id="IncomeDetailMap" type="com.lylink.moudel.income.vo.IncomeDetailVO">
        <id column="id" property="id"/>
        <result column="device_sn" property="deviceSn"/>
        <result column="device_alias" property="deviceAlias"/>
        <result column="date" property="date"/>
        <result column="upstream_income" property="upstreamIncome"/>
        <result column="commission" property="commission"/>
        <result column="actual_income" property="actualIncome"/>
        <result column="commission_rate" property="commissionRate"/>
        <result column="traffic_mb" property="trafficMb"/>
        <result column="online_duration" property="onlineDuration"/>
        <result column="online_duration_text" property="onlineDurationText"/>
        <result column="created_at" property="createdAt"/>
    </resultMap>

    <!-- 获取用户收益概览 -->
    <select id="getUserIncomeSummary" resultMap="IncomeSummaryMap">
        SELECT 
            COALESCE(today_stats.today_income, 0) as today_income,
            COALESCE(yesterday_stats.yesterday_income, 0) as yesterday_income,
            COALESCE(month_stats.month_income, 0) as month_income,
            COALESCE(last_month_stats.last_month_income, 0) as last_month_income,
            COALESCE(total_stats.total_income, 0) as total_income,
            COALESCE(total_stats.total_income, 0) as available_balance,
            COALESCE(today_stats.today_traffic, 0) as today_traffic,
            COALESCE(month_stats.month_traffic, 0) as month_traffic,
            COALESCE(total_stats.total_traffic, 0) as total_traffic,
            COALESCE(device_stats.online_device_count, 0) as online_device_count,
            COALESCE(device_stats.total_device_count, 0) as total_device_count,
            COALESCE(today_stats.today_online_duration, 0) as today_online_duration,
            CASE 
                WHEN COALESCE(yesterday_stats.yesterday_income, 0) = 0 THEN 0
                ELSE ROUND((COALESCE(today_stats.today_income, 0) - COALESCE(yesterday_stats.yesterday_income, 0)) / COALESCE(yesterday_stats.yesterday_income, 0) * 100, 2)
            END as income_growth_rate,
            CASE 
                WHEN COALESCE(yesterday_stats.yesterday_traffic, 0) = 0 THEN 0
                ELSE ROUND((COALESCE(today_stats.today_traffic, 0) - COALESCE(yesterday_stats.yesterday_traffic, 0)) / COALESCE(yesterday_stats.yesterday_traffic, 0) * 100, 2)
            END as traffic_growth_rate
        FROM (SELECT 1) as dummy
        LEFT JOIN (
            SELECT 
                SUM(actual_income) as today_income,
                SUM(traffic_mb) as today_traffic,
                SUM(online_duration) as today_online_duration
            FROM income_records 
            WHERE user_id = #{userId} AND date = CURDATE()
        ) today_stats ON 1=1
        LEFT JOIN (
            SELECT 
                SUM(actual_income) as yesterday_income,
                SUM(traffic_mb) as yesterday_traffic
            FROM income_records 
            WHERE user_id = #{userId} AND date = DATE_SUB(CURDATE(), INTERVAL 1 DAY)
        ) yesterday_stats ON 1=1
        LEFT JOIN (
            SELECT 
                SUM(actual_income) as month_income,
                SUM(traffic_mb) as month_traffic
            FROM income_records 
            WHERE user_id = #{userId} AND YEAR(date) = YEAR(CURDATE()) AND MONTH(date) = MONTH(CURDATE())
        ) month_stats ON 1=1
        LEFT JOIN (
            SELECT 
                SUM(actual_income) as last_month_income
            FROM income_records 
            WHERE user_id = #{userId} AND date >= DATE_SUB(DATE_SUB(CURDATE(), INTERVAL DAY(CURDATE())-1 DAY), INTERVAL 1 MONTH) 
                AND date < DATE_SUB(CURDATE(), INTERVAL DAY(CURDATE())-1 DAY)
        ) last_month_stats ON 1=1
        LEFT JOIN (
            SELECT 
                SUM(actual_income) as total_income,
                SUM(traffic_mb) as total_traffic
            FROM income_records 
            WHERE user_id = #{userId}
        ) total_stats ON 1=1
        LEFT JOIN (
            SELECT 
                COUNT(CASE WHEN status = 1 THEN 1 END) as online_device_count,
                COUNT(*) as total_device_count
            FROM devices 
            WHERE user_id = #{userId}
        ) device_stats ON 1=1
    </select>

    <!-- 获取用户收益明细列表 -->
    <select id="getUserIncomeDetails" resultMap="IncomeDetailMap">
        SELECT 
            ir.id,
            ir.device_sn,
            COALESCE(d.alias, CONCAT('设备', SUBSTRING(ir.device_sn, -4))) as device_alias,
            ir.date,
            ir.upstream_income,
            ir.commission,
            ir.actual_income,
            u.commission_rate,
            ir.traffic_mb,
            ir.online_duration,
            CASE 
                WHEN ir.online_duration >= 1440 THEN CONCAT(FLOOR(ir.online_duration / 1440), '天', FLOOR((ir.online_duration % 1440) / 60), '小时')
                WHEN ir.online_duration >= 60 THEN CONCAT(FLOOR(ir.online_duration / 60), '小时', ir.online_duration % 60, '分钟')
                ELSE CONCAT(ir.online_duration, '分钟')
            END as online_duration_text,
            ir.created_at
        FROM income_records ir
        LEFT JOIN devices d ON ir.device_sn = d.sn AND ir.user_id = d.user_id
        LEFT JOIN users u ON ir.user_id = u.id
        WHERE ir.user_id = #{userId}
        <if test="deviceSn != null and deviceSn != ''">
            AND ir.device_sn = #{deviceSn}
        </if>
        <if test="startDate != null">
            AND ir.date >= #{startDate}
        </if>
        <if test="endDate != null">
            AND ir.date <= #{endDate}
        </if>
        ORDER BY ir.date DESC, ir.created_at DESC
    </select>

    <!-- 获取收益趋势数据 -->
    <select id="getIncomeChartData" resultType="com.lylink.moudel.income.vo.IncomeChartVO$IncomeChartItem">
        SELECT 
            <choose>
                <when test="groupBy == 'month'">
                    DATE_FORMAT(date, '%Y-%m-01') as date,
                    DATE_FORMAT(date, '%Y年%m月') as dateLabel,
                </when>
                <otherwise>
                    date,
                    DATE_FORMAT(date, '%m月%d日') as dateLabel,
                </otherwise>
            </choose>
            SUM(actual_income) as income,
            SUM(traffic_mb) as traffic,
            SUM(online_duration) as onlineDuration,
            COUNT(DISTINCT device_sn) as deviceCount
        FROM income_records 
        WHERE user_id = #{userId} AND date >= #{startDate} AND date <= #{endDate}
        <choose>
            <when test="groupBy == 'month'">
                GROUP BY YEAR(date), MONTH(date)
                ORDER BY YEAR(date), MONTH(date)
            </when>
            <otherwise>
                GROUP BY date
                ORDER BY date
            </otherwise>
        </choose>
    </select>

    <!-- 获取设备收益分布 -->
    <select id="getDeviceIncomeDistribution" resultType="com.lylink.moudel.income.vo.IncomeChartVO$DeviceIncomeItem">
        SELECT 
            ir.device_sn as deviceSn,
            COALESCE(d.alias, CONCAT('设备', SUBSTRING(ir.device_sn, -4))) as deviceAlias,
            SUM(ir.actual_income) as income,
            ROUND(SUM(ir.actual_income) / total_income.total * 100, 2) as incomeRatio,
            SUM(ir.traffic_mb) as traffic,
            SUM(ir.online_duration) as onlineDuration
        FROM income_records ir
        LEFT JOIN devices d ON ir.device_sn = d.sn AND ir.user_id = d.user_id
        CROSS JOIN (
            SELECT SUM(actual_income) as total 
            FROM income_records 
            WHERE user_id = #{userId} AND date >= #{startDate} AND date <= #{endDate}
        ) total_income
        WHERE ir.user_id = #{userId} AND ir.date >= #{startDate} AND ir.date <= #{endDate}
        GROUP BY ir.device_sn, d.alias, total_income.total
        ORDER BY income DESC
    </select>

    <!-- 获取用户可提现余额 -->
    <select id="getUserAvailableBalance" resultType="java.math.BigDecimal">
        SELECT 
            COALESCE(SUM(ir.actual_income), 0) - COALESCE(SUM(wa.amount), 0) as available_balance
        FROM income_records ir
        LEFT JOIN withdraw_applications wa ON ir.user_id = wa.user_id AND wa.status IN ('approved', 'completed')
        WHERE ir.user_id = #{userId}
    </select>

    <!-- 批量插入收益记录 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO income_records (user_id, device_sn, date, upstream_income, commission, actual_income, traffic_mb, online_duration, sync_status)
        VALUES
        <foreach collection="records" item="record" separator=",">
            (#{record.userId}, #{record.deviceSn}, #{record.date}, #{record.upstreamIncome}, 
             #{record.commission}, #{record.actualIncome}, #{record.trafficMb}, #{record.onlineDuration}, #{record.syncStatus})
        </foreach>
    </insert>

    <!-- 更新同步状态 -->
    <update id="updateSyncStatus">
        UPDATE income_records 
        SET sync_status = #{syncStatus}, updated_at = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>
