package com.lylink.moudel.income.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lylink.common.constant.BusinessConstants;
import com.lylink.common.exception.CustomaizeExpetion;
import com.lylink.common.exception.ExceptionEnum;
import com.lylink.moudel.device.mapper.DeviceMapper;
import com.lylink.moudel.device.pojo.Device;
import com.lylink.moudel.device.service.UpstreamApiService;
import com.lylink.moudel.income.mapper.IncomeRecordMapper;
import com.lylink.moudel.income.pojo.IncomeRecord;
import com.lylink.moudel.income.service.IncomeService;
import com.lylink.moudel.income.vo.IncomeChartVO;
import com.lylink.moudel.income.vo.IncomeDetailVO;
import com.lylink.moudel.income.vo.IncomeSummaryVO;
import com.lylink.moudel.user.mapper.UserMapper;
import com.lylink.moudel.user.pojo.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 收益服务实现类
 */
@Slf4j
@Service
public class IncomeServiceImpl extends ServiceImpl<IncomeRecordMapper, IncomeRecord> implements IncomeService {

    @Autowired
    private IncomeRecordMapper incomeRecordMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private DeviceMapper deviceMapper;

    @Autowired
    private UpstreamApiService upstreamApiService;

    @Override
    public IncomeSummaryVO getUserIncomeSummary(Long userId) {
        return incomeRecordMapper.getUserIncomeSummary(userId);
    }

    @Override
    public IPage<IncomeDetailVO> getUserIncomeDetails(Long userId, int page, int size, 
                                                      String deviceSn, LocalDate startDate, LocalDate endDate) {
        Page<IncomeDetailVO> pageParam = new Page<>(page, size);
        return incomeRecordMapper.getUserIncomeDetails(pageParam, userId, deviceSn, startDate, endDate);
    }

    @Override
    public IncomeChartVO getIncomeChart(Long userId, String chartType, int days) {
        LocalDate endDate = LocalDate.now();
        LocalDate startDate;
        String groupBy;

        if ("monthly".equals(chartType)) {
            // 月收益图表，显示最近几个月
            startDate = endDate.minusMonths(days);
            groupBy = "month";
        } else {
            // 日收益图表，显示最近几天
            startDate = endDate.minusDays(days - 1);
            groupBy = "day";
        }

        IncomeChartVO chartVO = new IncomeChartVO();
        chartVO.setChartType(chartType);
        chartVO.setPeriod(startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + 
                         " 至 " + endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));

        // 获取趋势数据
        List<IncomeChartVO.IncomeChartItem> chartData = incomeRecordMapper.getIncomeChartData(userId, startDate, endDate, groupBy);
        chartVO.setChartData(chartData);

        // 获取设备收益分布
        List<IncomeChartVO.DeviceIncomeItem> deviceData = incomeRecordMapper.getDeviceIncomeDistribution(userId, startDate, endDate);
        chartVO.setDeviceData(deviceData);

        // 计算统计数据
        if (!chartData.isEmpty()) {
            BigDecimal totalIncome = chartData.stream()
                    .map(IncomeChartVO.IncomeChartItem::getIncome)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            chartVO.setTotalIncome(totalIncome);

            BigDecimal averageIncome = totalIncome.divide(BigDecimal.valueOf(chartData.size()), 4, RoundingMode.HALF_UP);
            chartVO.setAverageIncome(averageIncome);

            BigDecimal maxIncome = chartData.stream()
                    .map(IncomeChartVO.IncomeChartItem::getIncome)
                    .max(BigDecimal::compareTo)
                    .orElse(BigDecimal.ZERO);
            chartVO.setMaxIncome(maxIncome);

            BigDecimal minIncome = chartData.stream()
                    .map(IncomeChartVO.IncomeChartItem::getIncome)
                    .min(BigDecimal::compareTo)
                    .orElse(BigDecimal.ZERO);
            chartVO.setMinIncome(minIncome);
        } else {
            chartVO.setTotalIncome(BigDecimal.ZERO);
            chartVO.setAverageIncome(BigDecimal.ZERO);
            chartVO.setMaxIncome(BigDecimal.ZERO);
            chartVO.setMinIncome(BigDecimal.ZERO);
        }

        return chartVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean calculateAndSaveIncome(Long userId, String deviceSn, LocalDate date, 
                                          BigDecimal upstreamIncome, Long trafficMb, Integer onlineDuration) {
        // 检查记录是否已存在
        if (isIncomeRecordExists(userId, deviceSn, date)) {
            log.warn("收益记录已存在: userId={}, deviceSn={}, date={}", userId, deviceSn, date);
            return false;
        }

        // 获取用户信息
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new CustomaizeExpetion(ExceptionEnum.USER_NOT_FOUND);
        }

        // 计算抽成和实际收益
        BigDecimal commissionRate = user.getCommissionRate();
        BigDecimal commission = upstreamIncome.multiply(commissionRate).setScale(4, RoundingMode.HALF_UP);
        BigDecimal actualIncome = upstreamIncome.subtract(commission);

        // 创建收益记录
        IncomeRecord record = new IncomeRecord();
        record.setUserId(userId);
        record.setDeviceSn(deviceSn);
        record.setDate(date);
        record.setUpstreamIncome(upstreamIncome);
        record.setCommission(commission);
        record.setActualIncome(actualIncome);
        record.setTrafficMb(trafficMb != null ? trafficMb : 0L);
        record.setOnlineDuration(onlineDuration != null ? onlineDuration : 0);
        record.setSyncStatus(BusinessConstants.IncomeSyncStatus.SYNCED);

        boolean result = save(record);
        if (result) {
            log.info("收益记录保存成功: userId={}, deviceSn={}, date={}, actualIncome={}", 
                    userId, deviceSn, date, actualIncome);
        }

        return result;
    }

    @Override
    public BigDecimal getUserDayIncome(Long userId, LocalDate date) {
        return incomeRecordMapper.getUserDayIncome(userId, date);
    }

    @Override
    public BigDecimal getUserMonthIncome(Long userId, int year, int month) {
        return incomeRecordMapper.getUserMonthIncome(userId, year, month);
    }

    @Override
    public BigDecimal getUserTotalIncome(Long userId) {
        return incomeRecordMapper.getUserTotalIncome(userId);
    }

    @Override
    public BigDecimal getUserAvailableBalance(Long userId) {
        return incomeRecordMapper.getUserAvailableBalance(userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean syncDeviceIncome(Long userId, String deviceSn, LocalDate date) {
        try {
            // 检查设备是否属于该用户
            Device device = deviceMapper.findBySn(deviceSn);
            if (device == null || !userId.equals(device.getUserId())) {
                log.warn("设备不属于该用户: userId={}, deviceSn={}", userId, deviceSn);
                return false;
            }

            // 从上游API获取收益数据
            // 这里需要根据实际的上游API接口来实现
            // 暂时使用模拟数据
            BigDecimal upstreamIncome = simulateUpstreamIncome(deviceSn, date);
            Long trafficMb = simulateTrafficData(deviceSn, date);
            Integer onlineDuration = simulateOnlineDuration(deviceSn, date);

            return calculateAndSaveIncome(userId, deviceSn, date, upstreamIncome, trafficMb, onlineDuration);
        } catch (Exception e) {
            log.error("同步设备收益失败: userId={}, deviceSn={}, date={}", userId, deviceSn, date, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean syncAllUserIncome(LocalDate date) {
        try {
            List<Device> devices = deviceMapper.getAllBoundDevices();
            int successCount = 0;

            for (Device device : devices) {
                if (syncDeviceIncome(device.getUserId(), device.getSn(), date)) {
                    successCount++;
                }
            }

            log.info("批量同步收益完成: 日期={}, 总数={}, 成功={}", date, devices.size(), successCount);
            return successCount > 0;
        } catch (Exception e) {
            log.error("批量同步收益失败: date={}", date, e);
            return false;
        }
    }

    @Override
    public boolean isIncomeRecordExists(Long userId, String deviceSn, LocalDate date) {
        return incomeRecordMapper.countByUserDeviceDate(userId, deviceSn, date) > 0;
    }

    /**
     * 模拟上游收益数据
     * 实际项目中需要调用上游API获取真实数据
     */
    private BigDecimal simulateUpstreamIncome(String deviceSn, LocalDate date) {
        // 模拟收益数据，实际应该从上游API获取
        double baseIncome = Math.random() * 10 + 1; // 1-11元随机收益
        return BigDecimal.valueOf(baseIncome).setScale(4, RoundingMode.HALF_UP);
    }

    /**
     * 模拟流量数据
     */
    private Long simulateTrafficData(String deviceSn, LocalDate date) {
        // 模拟流量数据，实际应该从上游API获取
        return (long) (Math.random() * 1000 + 100); // 100-1100MB随机流量
    }

    /**
     * 模拟在线时长数据
     */
    private Integer simulateOnlineDuration(String deviceSn, LocalDate date) {
        // 模拟在线时长，实际应该从上游API获取
        return (int) (Math.random() * 1440 + 60); // 60-1500分钟随机在线时长
    }
}
