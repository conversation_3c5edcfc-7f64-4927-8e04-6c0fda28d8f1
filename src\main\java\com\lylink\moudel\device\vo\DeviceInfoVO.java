package com.lylink.moudel.device.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 设备信息VO
 */
@Data
public class DeviceInfoVO {

    /**
     * 设备ID
     */
    private Long id;

    /**
     * 设备序列号
     */
    private String sn;

    /**
     * 设备别名
     */
    private String alias;

    /**
     * 设备状态：1-在线，0-离线，-1-异常
     */
    private Integer status;

    /**
     * 设备状态描述
     */
    private String statusText;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 硬件信息
     */
    private Object hardwareInfo;

    /**
     * 绑定时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime bindTime;

    /**
     * 最后同步时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastSyncTime;

    /**
     * 今日收益
     */
    private BigDecimal todayIncome;

    /**
     * 总收益
     */
    private BigDecimal totalIncome;

    /**
     * 今日流量(MB)
     */
    private Long todayTraffic;

    /**
     * 总流量(MB)
     */
    private Long totalTraffic;

    /**
     * 在线时长(分钟)
     */
    private Integer onlineDuration;

    /**
     * 网络信息
     */
    private Object networkInfo;

    /**
     * 性能信息
     */
    private Object performanceInfo;
}
