package com.lylink.moudel.income.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lylink.moudel.income.pojo.IncomeRecord;
import com.lylink.moudel.income.vo.IncomeChartVO;
import com.lylink.moudel.income.vo.IncomeDetailVO;
import com.lylink.moudel.income.vo.IncomeSummaryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 收益记录Mapper接口
 */
@Mapper
public interface IncomeRecordMapper extends BaseMapper<IncomeRecord> {

    /**
     * 获取用户收益概览
     * 
     * @param userId 用户ID
     * @return 收益概览
     */
    IncomeSummaryVO getUserIncomeSummary(@Param("userId") Long userId);

    /**
     * 获取用户收益明细列表
     * 
     * @param page 分页参数
     * @param userId 用户ID
     * @param deviceSn 设备序列号（可选）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 收益明细列表
     */
    IPage<IncomeDetailVO> getUserIncomeDetails(@Param("page") Page<IncomeDetailVO> page,
                                               @Param("userId") Long userId,
                                               @Param("deviceSn") String deviceSn,
                                               @Param("startDate") LocalDate startDate,
                                               @Param("endDate") LocalDate endDate);

    /**
     * 获取收益趋势数据
     * 
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param groupBy 分组方式：day-按天，month-按月
     * @return 趋势数据
     */
    List<IncomeChartVO.IncomeChartItem> getIncomeChartData(@Param("userId") Long userId,
                                                           @Param("startDate") LocalDate startDate,
                                                           @Param("endDate") LocalDate endDate,
                                                           @Param("groupBy") String groupBy);

    /**
     * 获取设备收益分布
     * 
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 设备收益分布
     */
    List<IncomeChartVO.DeviceIncomeItem> getDeviceIncomeDistribution(@Param("userId") Long userId,
                                                                     @Param("startDate") LocalDate startDate,
                                                                     @Param("endDate") LocalDate endDate);

    /**
     * 检查收益记录是否存在
     * 
     * @param userId 用户ID
     * @param deviceSn 设备序列号
     * @param date 日期
     * @return 存在数量
     */
    @Select("SELECT COUNT(*) FROM income_records WHERE user_id = #{userId} AND device_sn = #{deviceSn} AND date = #{date}")
    int countByUserDeviceDate(@Param("userId") Long userId, @Param("deviceSn") String deviceSn, @Param("date") LocalDate date);

    /**
     * 获取用户指定日期的总收益
     * 
     * @param userId 用户ID
     * @param date 日期
     * @return 总收益
     */
    @Select("SELECT COALESCE(SUM(actual_income), 0) FROM income_records WHERE user_id = #{userId} AND date = #{date}")
    BigDecimal getUserDayIncome(@Param("userId") Long userId, @Param("date") LocalDate date);

    /**
     * 获取用户指定月份的总收益
     * 
     * @param userId 用户ID
     * @param year 年份
     * @param month 月份
     * @return 总收益
     */
    @Select("SELECT COALESCE(SUM(actual_income), 0) FROM income_records WHERE user_id = #{userId} AND YEAR(date) = #{year} AND MONTH(date) = #{month}")
    BigDecimal getUserMonthIncome(@Param("userId") Long userId, @Param("year") int year, @Param("month") int month);

    /**
     * 获取用户总收益
     * 
     * @param userId 用户ID
     * @return 总收益
     */
    @Select("SELECT COALESCE(SUM(actual_income), 0) FROM income_records WHERE user_id = #{userId}")
    BigDecimal getUserTotalIncome(@Param("userId") Long userId);

    /**
     * 获取用户可提现余额
     * 
     * @param userId 用户ID
     * @return 可提现余额
     */
    BigDecimal getUserAvailableBalance(@Param("userId") Long userId);

    /**
     * 批量插入收益记录
     * 
     * @param records 收益记录列表
     * @return 插入数量
     */
    int batchInsert(@Param("records") List<IncomeRecord> records);

    /**
     * 获取需要同步的收益记录
     * 
     * @param limit 限制数量
     * @return 收益记录列表
     */
    @Select("SELECT * FROM income_records WHERE sync_status = 0 ORDER BY created_at ASC LIMIT #{limit}")
    List<IncomeRecord> getUnsyncedRecords(@Param("limit") int limit);

    /**
     * 更新同步状态
     * 
     * @param ids 记录ID列表
     * @param syncStatus 同步状态
     * @return 更新数量
     */
    int updateSyncStatus(@Param("ids") List<Long> ids, @Param("syncStatus") Integer syncStatus);
}
