package com.lylink.moudel.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lylink.moudel.user.pojo.SmsCode;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;

/**
 * 短信验证码Mapper接口
 */
@Mapper
public interface SmsCodeMapper extends BaseMapper<SmsCode> {

    /**
     * 查询最新的有效验证码
     * 
     * @param phone 手机号
     * @param type 验证码类型
     * @return 验证码记录
     */
    @Select("SELECT * FROM sms_codes WHERE phone = #{phone} AND type = #{type} " +
            "AND used = 0 AND expire_time > NOW() ORDER BY created_at DESC LIMIT 1")
    SmsCode findLatestValidCode(@Param("phone") String phone, @Param("type") String type);

    /**
     * 标记验证码为已使用
     * 
     * @param id 验证码ID
     * @return 更新行数
     */
    @Update("UPDATE sms_codes SET used = 1 WHERE id = #{id}")
    int markAsUsed(@Param("id") Long id);

    /**
     * 统计今日发送次数
     * 
     * @param phone 手机号
     * @param startTime 开始时间
     * @return 发送次数
     */
    @Select("SELECT COUNT(*) FROM sms_codes WHERE phone = #{phone} AND created_at >= #{startTime}")
    int countTodaySendTimes(@Param("phone") String phone, @Param("startTime") LocalDateTime startTime);

    /**
     * 清理过期验证码
     * 
     * @param expireTime 过期时间
     * @return 清理数量
     */
    @Select("DELETE FROM sms_codes WHERE expire_time < #{expireTime}")
    int cleanExpiredCodes(@Param("expireTime") LocalDateTime expireTime);
}
