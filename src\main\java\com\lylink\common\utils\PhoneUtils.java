package com.lylink.common.utils;

import java.util.regex.Pattern;

/**
 * 手机号工具类
 */
public class PhoneUtils {

    /**
     * 中国大陆手机号正则表达式
     * 支持13x, 14x, 15x, 16x, 17x, 18x, 19x号段
     */
    private static final String PHONE_REGEX = "^1[3-9]\\d{9}$";
    
    private static final Pattern PHONE_PATTERN = Pattern.compile(PHONE_REGEX);

    /**
     * 验证手机号格式是否正确
     * 
     * @param phone 手机号
     * @return true-格式正确，false-格式错误
     */
    public static boolean isValidPhone(String phone) {
        if (StringUtils.isEmpty(phone)) {
            return false;
        }
        return PHONE_PATTERN.matcher(phone).matches();
    }

    /**
     * 手机号脱敏处理
     * 例如：13812345678 -> 138****5678
     * 
     * @param phone 手机号
     * @return 脱敏后的手机号
     */
    public static String maskPhone(String phone) {
        if (!isValidPhone(phone)) {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(7);
    }

    /**
     * 获取手机号归属地区号
     * 
     * @param phone 手机号
     * @return 区号前缀
     */
    public static String getPhonePrefix(String phone) {
        if (!isValidPhone(phone)) {
            return "";
        }
        return phone.substring(0, 3);
    }

    /**
     * 验证手机号是否为移动号段
     * 
     * @param phone 手机号
     * @return true-移动号段，false-非移动号段
     */
    public static boolean isChinaMobile(String phone) {
        if (!isValidPhone(phone)) {
            return false;
        }
        String prefix = phone.substring(0, 3);
        return prefix.matches("^(134|135|136|137|138|139|147|150|151|152|157|158|159|178|182|183|184|187|188|198)$");
    }

    /**
     * 验证手机号是否为联通号段
     * 
     * @param phone 手机号
     * @return true-联通号段，false-非联通号段
     */
    public static boolean isChinaUnicom(String phone) {
        if (!isValidPhone(phone)) {
            return false;
        }
        String prefix = phone.substring(0, 3);
        return prefix.matches("^(130|131|132|145|155|156|166|171|175|176|185|186|196)$");
    }

    /**
     * 验证手机号是否为电信号段
     * 
     * @param phone 手机号
     * @return true-电信号段，false-非电信号段
     */
    public static boolean isChinaTelecom(String phone) {
        if (!isValidPhone(phone)) {
            return false;
        }
        String prefix = phone.substring(0, 3);
        return prefix.matches("^(133|149|153|173|177|180|181|189|191|193|199)$");
    }

    /**
     * 获取运营商名称
     * 
     * @param phone 手机号
     * @return 运营商名称
     */
    public static String getCarrier(String phone) {
        if (isChinaMobile(phone)) {
            return "中国移动";
        } else if (isChinaUnicom(phone)) {
            return "中国联通";
        } else if (isChinaTelecom(phone)) {
            return "中国电信";
        } else {
            return "未知运营商";
        }
    }
}
