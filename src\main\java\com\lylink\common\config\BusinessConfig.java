package com.lylink.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 业务配置类
 */
@Data
@Component
@ConfigurationProperties(prefix = "business")
public class BusinessConfig {

    /**
     * 用户配置
     */
    private UserConfig user = new UserConfig();

    /**
     * 提现配置
     */
    private WithdrawConfig withdraw = new WithdrawConfig();

    /**
     * 短信验证码配置
     */
    private SmsCodeConfig smsCode = new SmsCodeConfig();

    /**
     * 设备配置
     */
    private DeviceConfig device = new DeviceConfig();

    @Data
    public static class UserConfig {
        /**
         * 用户最大设备绑定数
         */
        private Integer maxDeviceCount = 10;

        /**
         * 默认抽成比例
         */
        private BigDecimal defaultCommissionRate = new BigDecimal("0.1000");
    }

    @Data
    public static class WithdrawConfig {
        /**
         * 最小提现金额
         */
        private BigDecimal minAmount = new BigDecimal("10.00");

        /**
         * 最大提现金额
         */
        private BigDecimal maxAmount = new BigDecimal("10000.00");

        /**
         * 每日提现限额
         */
        private BigDecimal dailyLimit = new BigDecimal("50000.00");

        /**
         * 提现手续费率
         */
        private BigDecimal feeRate = new BigDecimal("0.0000");
    }

    @Data
    public static class SmsCodeConfig {
        /**
         * 验证码长度
         */
        private Integer length = 6;

        /**
         * 过期时间(分钟)
         */
        private Integer expireMinutes = 5;

        /**
         * 每日发送限制
         */
        private Integer dailyLimit = 10;
    }

    @Data
    public static class DeviceConfig {
        /**
         * 同步间隔(分钟)
         */
        private Integer syncInterval = 30;

        /**
         * 离线判定阈值(分钟)
         */
        private Integer offlineThreshold = 60;
    }
}
