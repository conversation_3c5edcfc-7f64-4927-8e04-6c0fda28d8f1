package com.lylink.moudel.device.pojo;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 设备实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("devices")
public class Device implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 设备ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 设备序列号
     */
    @TableField("sn")
    private String sn;

    /**
     * 设备别名
     */
    @TableField("alias")
    private String alias;

    /**
     * 绑定用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 绑定时间
     */
    @TableField("bind_time")
    private LocalDateTime bindTime;

    /**
     * 状态：1-正常，0-离线，-1-异常
     */
    @TableField("status")
    private Integer status;

    /**
     * 设备类型
     */
    @TableField("device_type")
    private String deviceType;

    /**
     * 硬件信息（JSON格式）
     */
    @TableField("hardware_info")
    private String hardwareInfo;

    /**
     * 最后同步时间
     */
    @TableField("last_sync_time")
    private LocalDateTime lastSyncTime;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}
