package com.lylink.moudel.withdraw.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lylink.moudel.withdraw.pojo.WithdrawApplication;
import com.lylink.moudel.withdraw.vo.WithdrawRecordVO;
import com.lylink.moudel.withdraw.vo.WithdrawSummaryVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 提现申请Mapper接口
 */
@Mapper
public interface WithdrawApplicationMapper extends BaseMapper<WithdrawApplication> {

    /**
     * 获取用户提现概览
     * 
     * @param userId 用户ID
     * @return 提现概览
     */
    WithdrawSummaryVO getUserWithdrawSummary(@Param("userId") Long userId);

    /**
     * 获取用户提现记录
     * 
     * @param page 分页参数
     * @param userId 用户ID
     * @param status 状态（可选）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 提现记录列表
     */
    IPage<WithdrawRecordVO> getUserWithdrawRecords(@Param("page") Page<WithdrawRecordVO> page,
                                                   @Param("userId") Long userId,
                                                   @Param("status") String status,
                                                   @Param("startDate") LocalDate startDate,
                                                   @Param("endDate") LocalDate endDate);

    /**
     * 统计用户今日提现金额
     * 
     * @param userId 用户ID
     * @param date 日期
     * @return 今日提现金额
     */
    @Select("SELECT COALESCE(SUM(amount), 0) FROM withdraw_applications " +
            "WHERE user_id = #{userId} AND DATE(apply_time) = #{date} " +
            "AND status IN ('approved', 'processing', 'completed')")
    BigDecimal getUserTodayWithdrawAmount(@Param("userId") Long userId, @Param("date") LocalDate date);

    /**
     * 统计用户总提现金额
     * 
     * @param userId 用户ID
     * @return 总提现金额
     */
    @Select("SELECT COALESCE(SUM(amount), 0) FROM withdraw_applications " +
            "WHERE user_id = #{userId} AND status IN ('approved', 'completed')")
    BigDecimal getUserTotalWithdrawAmount(@Param("userId") Long userId);

    /**
     * 统计用户待审核申请数
     * 
     * @param userId 用户ID
     * @return 待审核申请数
     */
    @Select("SELECT COUNT(*) FROM withdraw_applications WHERE user_id = #{userId} AND status = 'pending'")
    int getUserPendingCount(@Param("userId") Long userId);

    /**
     * 统计用户处理中申请数
     * 
     * @param userId 用户ID
     * @return 处理中申请数
     */
    @Select("SELECT COUNT(*) FROM withdraw_applications WHERE user_id = #{userId} AND status = 'processing'")
    int getUserProcessingCount(@Param("userId") Long userId);

    /**
     * 检查用户是否有进行中的提现申请
     * 
     * @param userId 用户ID
     * @return 进行中申请数
     */
    @Select("SELECT COUNT(*) FROM withdraw_applications WHERE user_id = #{userId} AND status IN ('pending', 'processing')")
    int getUserActiveWithdrawCount(@Param("userId") Long userId);

    /**
     * 更新申请状态
     * 
     * @param id 申请ID
     * @param status 新状态
     * @param auditReason 审核原因
     * @param auditAdminId 审核管理员ID
     * @return 更新行数
     */
    @Update("UPDATE withdraw_applications SET status = #{status}, audit_reason = #{auditReason}, " +
            "audit_admin_id = #{auditAdminId}, audit_time = NOW() WHERE id = #{id}")
    int updateApplicationStatus(@Param("id") String id, @Param("status") String status, 
                               @Param("auditReason") String auditReason, @Param("auditAdminId") Long auditAdminId);

    /**
     * 更新处理时间
     * 
     * @param id 申请ID
     * @param processTime 处理时间
     * @return 更新行数
     */
    @Update("UPDATE withdraw_applications SET process_time = #{processTime} WHERE id = #{id}")
    int updateProcessTime(@Param("id") String id, @Param("processTime") LocalDateTime processTime);

    /**
     * 获取待处理的提现申请
     * 
     * @param limit 限制数量
     * @return 申请列表
     */
    @Select("SELECT * FROM withdraw_applications WHERE status = 'approved' ORDER BY audit_time ASC LIMIT #{limit}")
    java.util.List<WithdrawApplication> getPendingProcessApplications(@Param("limit") int limit);
}
